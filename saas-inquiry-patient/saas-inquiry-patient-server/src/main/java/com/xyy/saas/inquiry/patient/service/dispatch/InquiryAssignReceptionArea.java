package com.xyy.saas.inquiry.patient.service.dispatch;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_NOT_MATCH_DEPT;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_NOT_MATCH_DOCTOR;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.enums.inquiry.ReceptionAreaSelectTypeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.patient.dal.redis.inquiry.InquiryRedisDao;
import com.xyy.saas.inquiry.patient.dal.redis.tenant.TenantRedisDao;
import com.xyy.saas.inquiry.patient.mq.message.InquiryTimeOutCheckEvent;
import com.xyy.saas.inquiry.patient.mq.producer.InquiryTimeOutCheckProducer;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.MathUtil;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/20 11:11
 * @Description: 为问诊单指定接诊大厅
 * @see InquiryAssignType previous
 */
@Component
@Slf4j
public class InquiryAssignReceptionArea extends InquiryBaseDispatch {

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Resource
    private InquiryRedisDao inquiryRedisDao;

    @Resource
    private TenantRedisDao tenantRedisDao;

    @Resource
    private InquiryTimeOutCheckProducer inquiryTimeOutCheckProducer;

    @Resource
    private ConfigApi configApi;

    @Resource
    private ThirdPartyPreInquiryMapper thirdPartyPreInquiryMapper;

    @Resource
    private DingService dingService;

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊单调度责任链【调度接诊大厅】", inquiryDto.getPref());
        // 校验接诊大厅是否有资源接诊
        checkReceptionAreaCanInquiry(inquiryDto);
        // 保存或更新参保人信息
        InquiryPatientInfoDO patientInfoDO = inquiryPatientInfoService.saveOrUpdatePatientInfo(PatientInfoConvert.INSTANCE.convertInquiryDTO2PatientDO(inquiryDto, inquiryDto.getTenantDto()),inquiryDto);
        // 持久化问诊单
        inquiryRecordMapper.insert(InquiryRecordConvert.INSTANCE.convertDTO2DOAndSetPatientPref(inquiryDto,patientInfoDO));
        // 持久化问诊单详情
        inquiryRecordDetailMapper.insert(InquiryRecordDetailConvert.INSTANCE.convertDTO2DO(inquiryDto.getInquiryRecordDetailDto()));
        // 修改三方预问诊中的问诊记录编号
        this.updateThirdPartyPreInquiry(inquiryDto.getThirdPartyPreInquiryId(), inquiryDto.getPref());
        // 将问诊推入门店待接诊缓存池
        tenantRedisDao.onDrugstoreInquiry(inquiryDto.getTenantId(), inquiryDto.getTenantDto().getEnvTag(), inquiryDto.getPref());
        // 发送问诊超时处理延迟消息
        inquiryTimeOutCheckProducer.sendMessage(InquiryTimeOutCheckEvent.builder().msg(inquiryDto.getPref()).build(),
            LocalDateTime.now().plusMinutes(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_TIME_OUT), 30)));
    }

    /**
     * 修改三方预问诊中的问诊记录编号
     *
     * @param thirdPartyPreInquiryId
     * @param pref
     */
    private void updateThirdPartyPreInquiry(Long thirdPartyPreInquiryId, String pref) {

        if (thirdPartyPreInquiryId == null || StringUtils.isBlank(pref)) {
            return;
        }

        thirdPartyPreInquiryMapper.updateById(ThirdPartyPreInquiryDO.builder().id(thirdPartyPreInquiryId).inquiryPref(pref).build());

    }

    /**
     * 获取接诊大厅的key 和 队列长度 ,并同步给问诊设置医院、科室
     */
    private void checkReceptionAreaCanInquiry(InquiryRecordDto inquiryDto) {
        TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO);
        // 医院科室信息是否为空
        if (ObjectUtil.isEmpty(inquiryDto.getHospitalDeptDto()) || CollectionUtils.isEmpty(inquiryDto.getHospitalDeptDto().getInquiryDeptList())) {
            // 未分配医院科室
            dingService.send(Markdown
                .title("问诊调度医院无匹配科室")
                .add("问诊单号", inquiryDto.getPref())
                .add("问诊门店", tenantDto.getName())
                .add("接诊医院",inquiryDto.getHospitalDeptDto().getHospitalName()+"("+inquiryDto.getHospitalDeptDto().getHospitalPref()+")")
                .add("诊断编码", JSON.toJSONString(inquiryDto.getInquiryRecordDetailDto().getDiagnosisCode()))
                .add("诊断名称", JSON.toJSONString(inquiryDto.getInquiryRecordDetailDto().getDiagnosisName()))
            );
            throw exception(INQUIRY_NOT_MATCH_DEPT);
        }
        HospitalDeptDto his = inquiryDto.getHospitalDeptDto();
        List<Dept> deptList = his.getInquiryDeptList();
        for (HospitalDeptDto.Dept dept : deptList) {
            // 获取当前科室对应医生队列的key
            String waitDoctorKey = RedisKeyConstants.getDoctorWaitPoolKey(his.getHospitalPref(), dept.getDeptPref(), inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType(), inquiryDto.getTenantDto().getEnvTag());
            // 如果当前接诊大厅无医生，则跳过
            if (RedisUtils.lSize(waitDoctorKey) <= 0) {
                continue;
            }
            return;
        }
        // 无医生接诊
        dingService.send(Markdown
            .title("问诊未匹配到可接诊医生")
            .add("问诊单号", inquiryDto.getPref())
            .add("问诊门店", tenantDto.getName())
            .add("接诊医院",inquiryDto.getHospitalDeptDto().getHospitalName()+"("+inquiryDto.getHospitalDeptDto().getHospitalPref()+")")
            .add("科室编码",inquiryDto.getHospitalDeptDto().getInquiryDeptList().stream().map(Dept::getDeptPref).collect(Collectors.joining(",")))
            .add("科室名称",inquiryDto.getHospitalDeptDto().getInquiryDeptList().stream().map(Dept::getDeptName).collect(Collectors.joining(",")))
        );
        // 都获取不到时，需要抛出暂无医生接诊的异常
        throw exception(INQUIRY_NOT_MATCH_DOCTOR);
    }

}
