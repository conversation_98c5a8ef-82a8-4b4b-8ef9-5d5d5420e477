package com.xyy.saas.inquiry.patient.convert.registration;

import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.registration.dto.MedicalRegistrationDto;
import com.xyy.saas.inquiry.patient.api.registration.dto.MedicalRegistrationInquiryUpdateDto;
import com.xyy.saas.inquiry.patient.api.registration.dto.transmission.MedicalRegistrationTransmissionRespDto;
import com.xyy.saas.inquiry.patient.controller.admin.registration.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.registration.MedicalRegistrationDO;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * @Author: cxy
 * @Date: 2024/10/25 16:02
 * @Description: 就诊登记
 */
@Mapper
public interface RegistrationConvert {

    RegistrationConvert INSTANCE = Mappers.getMapper(RegistrationConvert.class);


    default MedicalRegistrationDO convertInquirySaveDo(InquiryRecordDto saveDto, MedicalRegistrationTransmissionRespDto transmissionDto) {
        MedicalRegistrationDO medicalRegistrationDO = convertTransmission(transmissionDto);
        convertFillDo(saveDto, medicalRegistrationDO);
        return medicalRegistrationDO;
    }

    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "bizId", source = "pref")
    @Mapping(target = "bizType", expression = "java(com.xyy.saas.inquiry.enums.system.BizTypeEnum.HYWZ.getCode())")
    @Mapping(target = "hospitalPref", expression = "java(saveDto.getChoiceHospitalList().getFirst())")
    void convertFillDo(InquiryRecordDto saveDto, @MappingTarget MedicalRegistrationDO medicalRegistrationDO);

    MedicalRegistrationDO convertTransmission(MedicalRegistrationTransmissionRespDto transmissionDto);

    default MedicalRegistrationDO convert(InquiryRecordDO inquiryRecordDO, MedicalRegistrationTransmissionRespDto transmissionDto) {
        inquiryRecordDO = Optional.ofNullable(inquiryRecordDO).orElse(new InquiryRecordDO());
        return MedicalRegistrationDO.builder().deptName(inquiryRecordDO.getDeptName())
            .deptPref(inquiryRecordDO.getDeptPref())
            .hospitalName(inquiryRecordDO.getHospitalName())
            .hospitalPref(inquiryRecordDO.getHospitalPref())
            .status(transmissionDto.getStatus()).build();
    }

    MedicalRegistrationRespVO convert(MedicalRegistrationDO registrationDO);

    MedicalRegistrationDto convertDto(MedicalRegistrationRespVO registrationInfo);

    @Mapping(target = "idCardNo", source = "patientIdCard")
    @Mapping(target = "idCard", source = "patientIdCard")
    @Mapping(target = "fullName", source = "patientName")
    @Mapping(target = "userId", expression = "java(cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId())")
    @Mapping(target = "businessNo", source = "pref")
    @Mapping(target = "inquiryPref", source = "pref")
    @Mapping(target = "hospitalPref", expression = "java(saveDto.getChoiceHospitalList().getFirst())")
    RegistrationTransmitterDTO convertTransmission(InquiryRecordDto saveDto);

    default RegistrationTransmitterDTO convertTransmission(MedicalRegistrationInquiryUpdateDto updateDto, MedicalRegistrationDO registrationDO) {
        RegistrationTransmitterDTO transmissionReqDataBaseDTO = new RegistrationTransmitterDTO()
            .setMedicalVisitId(registrationDO.getMedicalVisitId())
            .setStatus(updateDto.getStatus());

        transmissionReqDataBaseDTO.setIdCard(registrationDO.getPatientIdCard())
            .setFullName(registrationDO.getPatientName())
            .setBusinessNo(updateDto.getInquiryPref());
        return transmissionReqDataBaseDTO;
    }
}
