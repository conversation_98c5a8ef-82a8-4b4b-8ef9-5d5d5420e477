package com.xyy.saas.inquiry.patient.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.patient.mq.message.InquiryTimeOutCheckEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/31 17:32
 * @Description: 问诊超时检查消息生产者
 */
@Component
@EventBusProducer(
    topic = InquiryTimeOutCheckEvent.TOPIC
)
public class InquiryTimeOutCheckProducer extends EventBusRocketMQTemplate {

}
