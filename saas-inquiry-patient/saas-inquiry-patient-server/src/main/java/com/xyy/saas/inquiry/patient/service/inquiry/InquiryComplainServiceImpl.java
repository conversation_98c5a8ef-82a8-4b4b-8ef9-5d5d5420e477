package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryComplainSaveReqVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryComplainConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryComplainDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryComplainMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;



/**
 * 问诊投诉记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryComplainServiceImpl implements InquiryComplainService {

    @Resource
    private InquiryComplainMapper inquiryComplainMapper;

    @Resource
    private InquiryService inquiryService;

    @Resource
    private InquiryDoctorApi inquiryDoctorApi;



    @Override
    public Long createInquiryComplain(InquiryComplainSaveReqVO createReqVO) {
        // 查询问诊单号
        InquiryRecordDO inquiryRecordDO = inquiryService.getInquiryByPref(createReqVO.getInquiryPref());
        if(ObjectUtil.isEmpty(inquiryRecordDO) || StringUtils.isBlank(inquiryRecordDO.getDoctorPref())){
            return null;
        }
        //查询医生信息
        InquiryDoctorDto doctorDto = inquiryDoctorApi.getInquiryDoctorByDoctorPref(inquiryRecordDO.getDoctorPref());
        if(ObjectUtil.isEmpty(doctorDto)){
            return null;
        }
        Long complainUserId = WebFrameworkUtils.getLoginUserId();
        Long beComplainUserId = ObjectUtil.equals(doctorDto.getUserId(),complainUserId) ? Long.valueOf(inquiryRecordDO.getCreator()) : doctorDto.getUserId();
        // 插入
        InquiryComplainDO inquiryComplain = InquiryComplainConvert.INSTANCE.convertVO2DO(createReqVO, complainUserId,beComplainUserId);
        inquiryComplainMapper.insert(inquiryComplain);
        // 返回
        return inquiryComplain.getId();
    }
}