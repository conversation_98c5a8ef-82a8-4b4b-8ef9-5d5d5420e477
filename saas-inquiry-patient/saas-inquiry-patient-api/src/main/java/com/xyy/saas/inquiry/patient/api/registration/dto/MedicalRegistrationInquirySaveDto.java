package com.xyy.saas.inquiry.patient.api.registration.dto;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/10 13:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "问诊就诊登记saveDto")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class MedicalRegistrationInquirySaveDto implements Serializable {

    @Schema(description = "问诊编号")
    @NotEmpty(message = "问诊编号不可为空")
    private String inquiryPref;

    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "患者pref")
    @NotEmpty(message = "患者pref不可为空")
    private String patientPref;

    @Schema(description = "患者姓名")
    @NotEmpty(message = "患者姓名不可为空")
    private String patientName;

    @Schema(description = "患者手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号")
    @NotEmpty(message = "患者身份证号可为空")
    private String patientIdCard;

    @Schema(description = "问诊医院编码")
    private String hospitalPref;

    // @Schema(description = "预约登记时间")
    // private LocalDateTime bookTime;
    //
    // @Schema(description = "预约登记结束时间")
    // private LocalDateTime bookEndTime;
}
