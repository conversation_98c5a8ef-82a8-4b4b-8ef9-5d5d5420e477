package com.xyy.saas.inquiry.im.server.controller.app.user;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserRespVO;
import com.xyy.saas.inquiry.im.server.service.user.InquiryImUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP + PC - 腾讯IM用户")
@RestController
@RequestMapping(value = {"/admin-api/kernel/im/inquiry-im-user", "/app-api/kernel/im/inquiry-im-user"})
@Validated
public class InquiryImUserController {

    @Resource
    private InquiryImUserService inquiryImUserService;

    @GetMapping("/get")
    @Operation(summary = "根据userid获取IM+TRTC配置")
    @Parameter(name = "clientChannelType", description = "客户端类型 0-APP  1-PC  2-小程序", required = true, example = "0")
    public CommonResult<InquiryImUserRespVO> getInquiryImUser(@RequestParam("clientChannelType") Integer clientChannelType) {
        return success(inquiryImUserService.getInquiryImUser(getLoginUserId(), ClientChannelTypeEnum.fromCode(clientChannelType)));
    }


}