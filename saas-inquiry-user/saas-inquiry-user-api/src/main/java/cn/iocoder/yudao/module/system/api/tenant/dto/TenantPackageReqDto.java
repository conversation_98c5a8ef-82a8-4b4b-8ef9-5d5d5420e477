package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/27 17:26
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageReqDto implements Serializable {

    @Schema(description = "套餐IDs", example = "99")
    private List<Long> packageIds;

    @Schema(description = "套餐包IDs", example = "99")
    private List<Long> tenantPackageIds;

    /**
     * 门店id
     */
    private Long tenantId;

    @Schema(description = "业务线", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    @Schema(description = "开通套餐状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否生效", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer effective;

    @Schema(description = "需要有效+待生效得数据，结束时间 > now()")
    private boolean endTimeEffective;
}
