package cn.iocoder.yudao.module.system.api.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import cn.iocoder.yudao.module.system.convert.tenant.TenantThirdAppConvert;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppPageReqVO;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import com.xyy.saas.inquiry.user.server.service.tenant.TenantThirdAppService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
public class TenantThirdAppApiImpl implements TenantThirdAppApi{

    @Resource
    @Lazy
    private TenantThirdAppService tenantThirdAppService;

    @Override
    public List<TenantThirdAppRespDto> getByTenantId(Long tenantId) {

        if (tenantId == null || tenantId < 0) {
            return Lists.newArrayList();
        }

        TenantThirdAppPageReqVO tenantThirdAppPageReqVO = new TenantThirdAppPageReqVO();
        tenantThirdAppPageReqVO.setTenantId(tenantId);
        List<TenantThirdAppDO> tenantThirdAppDOS = tenantThirdAppService.queryByCondition(tenantThirdAppPageReqVO);

        if (CollUtil.isEmpty(tenantThirdAppDOS)) {
            return Lists.newArrayList();
        }

        return TenantThirdAppConvert.INSTANCE.convertDoList2RespDtoList(tenantThirdAppDOS);
    }

    @Override
    public List<TenantThirdAppRespDto> getByTenantIdList(List<Long> tenantIdList) {

        if (CollUtil.isEmpty(tenantIdList)) {
            return Lists.newArrayList();
        }

        TenantThirdAppPageReqVO tenantThirdAppPageReqVO = new TenantThirdAppPageReqVO();
        tenantThirdAppPageReqVO.setTenantIdList(tenantIdList);
        List<TenantThirdAppDO> tenantThirdAppDOS = tenantThirdAppService.queryByCondition(tenantThirdAppPageReqVO);

        if (CollUtil.isEmpty(tenantThirdAppDOS)) {
            return Lists.newArrayList();
        }

        return TenantThirdAppConvert.INSTANCE.convertDoList2RespDtoList(tenantThirdAppDOS);
    }

    @Override
    public List<TenantThirdAppRespDto> getListByCondition(TenantThirdAppReqDto tenantThirdAppReqDto) {

        TenantThirdAppPageReqVO tenantThirdAppPageReqVO = new TenantThirdAppPageReqVO();
        tenantThirdAppPageReqVO.setIdList(tenantThirdAppReqDto.getIdList());
        List<TenantThirdAppDO> tenantThirdAppDOS = tenantThirdAppService.queryByCondition(tenantThirdAppPageReqVO);

        if (CollUtil.isEmpty(tenantThirdAppDOS)) {
            return Lists.newArrayList();
        }

        return TenantThirdAppConvert.INSTANCE.convertDoList2RespDtoList(tenantThirdAppDOS);
    }
}
