package cn.iocoder.yudao.module.system.service.permission;

import static java.util.Collections.singleton;

import cn.iocoder.yudao.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserPermissionVO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserRoleVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 权限 Service 接口
 * <p>
 * 提供用户-角色、角色-菜单、角色-部门的关联权限处理
 *
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 判断是否有权限，任一一个即可
     *
     * @param userId      用户编号
     * @param permissions 权限
     * @return 是否
     */
    boolean hasAnyPermissions(Long userId, String... permissions);

    /**
     * 判断是否有角色，任一一个即可
     *
     * @param roles 角色数组
     * @return 是否
     */
    boolean hasAnyRoles(Long userId, String... roles);

    // ========== 角色-菜单的相关方法  ==========

    /**
     * 设置角色菜单
     *
     * @param roleId  角色编号
     * @param menuIds 菜单编号集合
     */
    void assignRoleMenu(Long roleId, Set<Long> menuIds);

    /**
     * 处理角色删除时，删除关联授权数据
     *
     * @param roleId 角色编号
     */
    void processRoleDeleted(Long roleId);

    /**
     * 处理菜单删除时，删除关联授权数据
     *
     * @param menuId 菜单编号
     */
    void processMenuDeleted(Long menuId);

    /**
     * 获得角色拥有的菜单编号集合
     *
     * @param roleId 角色编号
     * @return 菜单编号集合
     */
    default Set<Long> getRoleMenuListByRoleId(Long roleId) {
        return getRoleMenuListByRoleId(singleton(roleId));
    }

    /**
     * 获得角色们拥有的菜单编号集合
     *
     * @param roleIds 角色编号数组
     * @return 菜单编号集合
     */
    Set<Long> getRoleMenuListByRoleId(Collection<Long> roleIds);

    /**
     * 获得拥有指定菜单的角色编号数组，从缓存中获取
     *
     * @param menuId 菜单编号
     * @return 角色编号数组
     */
    Set<Long> getMenuRoleIdListByMenuIdFromCache(Long menuId);

    // ========== 用户-角色的相关方法  ==========

    /**
     * 设置用户角色
     *
     * @param userId  角色编号
     * @param roleIds 角色编号集合
     */
    void assignUserRole(Long userId, Set<Long> roleIds);

    /**
     * 设置用户角色前置见擦汗
     *
     * @param userId  角色编号
     * @param roleIds 角色编号集合
     */
    void assignUserRoleCheck(Long userId, Set<Long> roleIds);

    /**
     * 处理用户删除时，删除关联授权数据
     *
     * @param userId 用户编号
     */
    void processUserDeleted(Long userId);

    /**
     * 获得拥有多个角色的用户编号集合
     *
     * @param roleIds 角色编号集合
     * @return 用户编号集合
     */
    Set<Long> getUserRoleIdListByRoleId(Collection<Long> roleIds);

    /**
     * 获得用户拥有的角色编号集合
     *
     * @param userId 用户编号
     * @return 角色编号集合
     */
    Set<Long> getUserRoleIdListByUserId(Long userId);

    /**
     * 获得用户拥有的角色编号集合，从缓存中获取
     *
     * @param userId 用户编号
     * @return 角色编号集合
     */
    Set<Long> getUserRoleIdListByUserIdFromCache(Long userId);


    /**
     * 问诊设置用户角色(仅用于 签名管理-C核对/A调配/D发药/P药师)
     *
     * @param userId       角色编号
     * @param roleIds      角色编号集合
     * @param roleRangeIds 处理的角色id集合
     */
    void assignUserRoleWithRoleRanges(Long userId, Set<Long> roleIds, Set<Long> roleRangeIds);


    /**
     * 根据userIds 获取用户角色ids
     *
     * @param userIds 根据用户userIds获取用户角色ids
     * @return
     */
    List<UserRoleVO> getUserRoleByTenantUserIds(Collection<Long> userIds);

    /**
     * 获取用户所有的角色
     *
     * @param id userid
     * @return 角色列表
     */
    List<RoleDO> getRoleByUser(Long id);

    // ========== 用户-部门的相关方法  ==========

    /**
     * 设置角色的数据权限
     *
     * @param roleId           角色编号
     * @param dataScope        数据范围
     * @param dataScopeDeptIds 部门编号数组
     */
    void assignRoleDataScope(Long roleId, Integer dataScope, Set<Long> dataScopeDeptIds);

    /**
     * 获得登陆用户的部门数据权限
     *
     * @param userId 用户编号
     * @return 部门数据权限
     */
    DeptDataPermissionRespDTO getDeptDataPermission(Long userId);

    /**
     * 查询用户角色ids
     *
     * @param userId    用户id
     * @param roleCodes 角色code
     * @return
     */
    List<RoleDO> selectUserRoleByUserIdRoleCodes(Long userId, Set<String> roleCodes);

    /**
     * 直接赋予角色
     *
     * @param userId    用户
     * @param roleCodes 角色Code assignUserRoleWithSystemRoleCodes
     */
    void assignUserRoleWithSystemRoleCodes(Long userId, List<String> roleCodes);

    /**
     * 批量门店赋予用户角色
     *
     * @param userId       用户id
     * @param tenantIdList 门店列表
     * @param roleIds      角色ids
     */
    void assignUserRoleBatchTenant(Long userId, List<Long> tenantIdList, List<Long> roleIds);

    /**
     * 批量门店解除用户角色
     *
     * @param userId       用户id
     * @param tenantIdList 门店列表
     * @param roleIds      角色ids
     */
    void deleteUserRoleBatchTenant(Long userId, List<Long> tenantIdList, List<Long> roleIds);

    void evictUserRoles(Long userId, Long tenantId);

    /**
     * 解除用户系统角色
     *
     * @param userId   用户id
     * @param tenantId 门店id
     * @param roleCode 系统角色id
     */
    void deleteUserSystemRole(Long userId, Long tenantId, String roleCode);

    /**
     * 根据角色id查询当前门店用户列表
     *
     * @param roleIds 角色ids 可用的
     * @return
     */
    List<UserRoleVO> getUserListByRoleIds(Set<Long> roleIds);


    /**
     * 根据用户id获取用户权限信息
     * @param userId 用户id
     * @return
     */
    UserPermissionVO getUserPermission(Long userId);
}
