package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.system.framework.operatelog.core.DeptParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.PostParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.SexParseFunction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xyy.saas.inquiry.constant.SystemConstant;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "管理后台 - 用户创建/修改 Request VO")
@Data
@Accessors(chain = true)
public class UserSaveReqVO {

    @Schema(description = "用户编号", example = "1024")
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    @Schema(description = "登录账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    // @NotBlank(message = "登录账号不能为空")
//    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "登录账号由 数字、字母 组成")
//     @Size(min = 4, max = 30, message = "登录账号长度为 4-30 个字符")
    @DiffLogField(name = "登录账号")
    private String username;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @Size(max = 30, message = "姓名长度不能超过30个字符")
    @DiffLogField(name = "姓名")
    private String nickname;

    @Schema(description = "备注", example = "我是一个用户")
    @DiffLogField(name = "备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    @Schema(description = "部门编号", example = "我是一个用户")
    @DiffLogField(name = "部门", function = DeptParseFunction.NAME)
    private Long deptId;

    @Schema(description = "岗位编号数组", example = "1")
    @DiffLogField(name = "岗位", function = PostParseFunction.NAME)
    private Set<Long> postIds;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    @DiffLogField(name = "用户邮箱")
    private String email;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    @DiffLogField(name = "手机号码")
    private String mobile;

    @Schema(description = "身份证号", example = "******************")
    @Size(max = 18, message = "身份证号不能超过 18 个字符")
    @DiffLogField(name = "身份证号")
    // @NotEmpty(message = "身份证号不能为空")
    private String idCard;


    @Schema(description = "状态，0-启用  1-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = UserAccountStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

    @Schema(description = "用户性别，参见 SexEnum 枚举类", example = "1")
    @DiffLogField(name = "用户性别", function = SexParseFunction.NAME)
    private Integer sex;

    @Schema(description = "用户头像", example = "https://www.iocoder.cn/xxx.png")
    @DiffLogField(name = "用户头像")
    private String avatar;

    @Schema(description = "关联的门店ids", example = "1")
    private Set<Long> tenantIds;

    // ========== 仅【创建】时，需要传递的字段 ==========

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Pattern(regexp = SystemConstant.PASSWORD_PATTERN_REGEXP, message = SystemConstant.PASSWORD_PATTERN_REGEXP_MESSAGE)
    private String password;

    /**
     * 是否绑定当前门店关系 默认是 超管创建门店药师时 不绑定
     */
    private boolean bindTenant = true;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer needClockIn;

    /**
     * 检查员工绑定关系，药师创建除外-
     */
    private boolean checkRelation = true;

    @AssertTrue(message = "登录账号长度为 4-30 个字符")
    @JsonIgnore
    public boolean userNameValid() {

        if (StringUtils.isBlank(username)) {
            return true;
        }

        return StringUtils.length(username) >= 4 && StringUtils.length(username) <= 30;
    }

}
