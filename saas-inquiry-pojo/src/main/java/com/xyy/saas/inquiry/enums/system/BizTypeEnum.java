package com.xyy.saas.inquiry.enums.system;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 系统业务类型
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午7:51
 */
@Getter
@RequiredArgsConstructor
public enum BizTypeEnum implements IntArrayValuable {

    HYWZ(0, "荷叶问诊"),
    //快速问诊
    ZHL(1, "智慧脸"),

    MEDICARE(2, "医保"),

    CLINIC(3, "诊所"),

    ;


    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(BizTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static BizTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(HYWZ);
    }

}