package com.xyy.saas.inquiry.enums.doctor;

import lombok.Getter;

/**
 * 医生过滤链枚举
 */
@Getter
public enum DoctorFilterChainEnum {

    INTERVAL_DOCTOR_FILTERCHAIN("prescriptionIntervalFilterChain","provinceFilterChain","医生开方间隔过滤"),
    ILK_PROVINCE_DOCTOR_FILTERCHAIN("provinceFilterChain","repeatDistributeFilterChain","同省医生过滤"),
    REPEAT_DISTRIBUTE_FILTERCHAIN("repeatDistributeFilterChain","grabFirstFilterChain","真人问诊已派单医生过滤"),
    GRAB_FIRST_DOCTOR_FILTERCHAIN("grabFirstFilterChain","inquirySendFilterChain","抢单优先策略开启时过滤抢单医生"),
    SEND_DOCTOR_FILTERCHAIN("inquirySendFilterChain","autoGrabCheckChain","派单医生数量过滤"),
    AUTO_GRAB_CHECK_FILTERCHAIN("autoGrabCheckChain","autoVideoChooseChain","自动抢单过滤"),
    AUTO_VIDEO_CHOOSE_FILTERCHAIN("autoVideoChooseChain","","自动开方录屏选取"),
    ;


    private String node;
    private String next;
    private String desc;

    DoctorFilterChainEnum(String node, String next, String desc) {
        this.node = node;
        this.next = next;
        this.desc = desc;
    }
}
