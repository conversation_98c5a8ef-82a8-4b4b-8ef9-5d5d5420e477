package com.xyy.saas.inquiry.pojo.condition;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.util.SpelParserUtil;
import java.util.Arrays;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 条件类型：支持地区、年龄、药品分类、开单科室、慢病、处方类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Getter
public enum ConditionRuleType {
    AREA("area", "地区") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return CollUtil.containsAny(paramDto.getAreaCodes(), Arrays.stream(StringUtils.split(condition, ",")).toList());
        }
    },
    AGE("age", "年龄") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return SpelParserUtil.parseBoolean("T(cn.hutool.core.util.NumberUtil).parseDouble(" + AGE.type + ")" + op.getEl() + condition, paramDto);
        }
    },
    MEDICINE_TYPE("medicineType", "药品类型") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return StringUtils.equalsIgnoreCase(paramDto.getMedicineType() == null ? "" : paramDto.getMedicineType().toString(), condition);
        }
    },
    DRUG_CATEGORY("drugCategory", "药品分类") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return CollUtil.contains(paramDto.getDrugCategories(), condition);
        }
    },
    BILLING_DEPT("billingDept", "开单科室") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return StringUtils.equalsIgnoreCase(condition, paramDto.getDept());
        }
    },
    SLOW_DISEASE("slowDisease", "慢病") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return StringUtils.equalsIgnoreCase(condition, paramDto.getSlowDisease() == null ? "" : paramDto.getSlowDisease().toString());
        }
    },
    PRESCRIPTION_TYPE("prescriptionType", "处方类型") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return StringUtils.equalsIgnoreCase(condition, paramDto.getPrescriptionType());
        }
    },
    DIAGNOSIS_COUNT("diagnosisCount", "诊断数量") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return SpelParserUtil.parseBoolean(DIAGNOSIS_COUNT.type + op.getEl() + condition, paramDto);
        }
    },
    DIAGNOSIS_TRANS_DEPT_COUNT("diagnosisTransDeptCount", "诊断跨科室数量") {
        @Override
        public boolean match(String condition, ConditionRuleOp op, ConditionParamDto paramDto) {
            return SpelParserUtil.parseBoolean(DIAGNOSIS_TRANS_DEPT_COUNT.type + op.getEl() + condition, paramDto);
        }
    },
    ;

    private String type;
    private String desc;

    /**
     * 根据规则条件匹配
     *
     * @param condition 条件 eg: 60  , 130100
     * @param op        操作条件 eg : >  , <=
     * @param value     计算对象
     * @return
     */
    public abstract boolean match(String condition, ConditionRuleOp op, ConditionParamDto value);

    ConditionRuleType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ConditionRuleType fromType(String type) {
        return Arrays.stream(values())
            .filter(t -> StringUtils.equalsIgnoreCase(t.getType(), type))
            .findFirst()
            .orElse(null);
    }
}
