package com.xyy.saas.inquiry.enums.user;

import java.util.List;

public enum CertificateTypeEnum {

    ID_CARD_FRONT(1, "身份证正", "getIdCardFrontImgUrl", "setIdCardFrontImgUrl", String.class),
    ID_CARD_REVERSE(2, "身份证反", "getIdCardReverseImgUrl", "setIdCardReverseImgUrl", String.class),
    VERIFY_IMG(3, "verifiyImg", "getVerifiyImgUrl", "setVerifiyImgUrl", String.class),
    TITLE_IMG(4, "职称证", "getTitleImgUrl", "setTitleImgUrl", String.class),
    PERSONAL_IMG(5, "personalImg", "getPersonalImgUrl", "setPersonalImgUrl", String.class),
    OCCUPATION_IMG(6, "执业证", "getOccupationImgUrls", "setOccupationImgUrls", List.class),
    QUALIFICATION_IMG(7, "资格证", "getQualificationImgUrls", "setQualificationImgUrls", List.class),
    HN_CONTRACT(8, "hnContractImg", "getHnContractImgUrls", "setHnContractImgUrls", List.class),
    CD_CONTRACT(9, "cdContractImg", "getCdContractImgUrls", "setCdContractImgUrls", List.class),
    WH_CONTRACT(10, "whContractImg", "getWhContractImgUrls", "setWhContractImgUrls", List.class),
    ORG_ELECTRON_SIGN(11, "orgElectronSignImg", "getOrgElectronSignImgUrl", "setOrgElectronSignImgUrl", String.class),
    DOCTOR_ELECTRON_SIGN(12, "doctorElectronSignImg", "getDoctorElectronSignImgUrl", "setDoctorElectronSignImgUrl", String.class),

    ZJZ(13, "证件照", "getDoctorElectronSignImgUrl", "setDoctorElectronSignImgUrl", String.class),
    ZCZ(14, "注册证", "getDoctorElectronSignImgUrl", "setDoctorElectronSignImgUrl", String.class),
    ;


    private final Integer type;
    private final String description;
    private final String getMethodName;
    private final String setMethodName;
    private final Class<?> classType;


    CertificateTypeEnum(Integer type, String description, String getMethodName, String setMethodName, Class<?> classType) {
        this.type = type;
        this.description = description;
        this.getMethodName = getMethodName;
        this.setMethodName = setMethodName;
        this.classType = classType;
    }

    public static CertificateTypeEnum getByType(Integer type) {
        //根据type获取对应的枚举
        for (CertificateTypeEnum item : CertificateTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getGetMethodName() {
        return getMethodName;
    }

    public String getSetMethodName() {
        return setMethodName;
    }

    public Class<?> getClassType() {
        return classType;
    }
}
