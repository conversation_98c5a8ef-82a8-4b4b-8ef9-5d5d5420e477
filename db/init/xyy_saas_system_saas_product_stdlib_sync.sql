insert into xyy_saas_system.saas_product_stdlib_sync (id, guid, type, status, start_id, end_id, current, start_time, end_time, error_msg, remark, creator, create_time, updater, update_time, deleted)
values  (1, '5e3b474c-c28d-4402-a55f-0bb9731b899f', 1, 3, 0, 0, 1500, '2025-03-19 17:30:03', '2025-03-19 17:38:46', '
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Duplicate entry ''937627664'' for key ''PRIMARY''
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper.insert (batch index #1) failed. Cause: java.sql.BatchUpdateException: Duplicate entry ''937627664'' for key ''PRIMARY''', null, '', '2025-03-19 17:30:03', '', '2025-03-19 17:30:03', false),
        (2, 'bce30311-a978-46ea-a1d7-ce0d70c17949', 1, 2, 0, 0, 1500, '2025-03-19 17:56:40', '2025-03-19 18:10:51', null, null, '1', '2025-03-19 17:56:40', '1', '2025-03-19 17:56:40', false),
        (3, 'e42a3812-63a5-4c2f-a9fc-5f00686e9137', 1, 2, 0, 0, -162000, '2025-03-19 18:13:52', '2025-03-19 21:34:29', null, null, '1', '2025-03-19 18:13:52', '1', '2025-03-19 18:13:52', false),
        (4, 'f34509a1-6ba3-4567-bca2-edfaf3aa9fba', 1, 2, 0, 0, 501, '2025-03-20 11:22:22', '2025-03-20 11:22:22', null, null, '1', '2025-03-20 11:22:22', '1', '2025-03-20 13:31:37', false),
        (5, '903fd7d0-0832-43db-bf7c-46f0ca4b9298', 1, 2, 0, 0, 5500, '2025-03-20 13:31:45', '2025-03-20 18:21:03', null, null, '1', '2025-03-20 13:31:45', '0', '2025-03-20 18:16:09', false),
        (6, 'c0efaf77-366c-4fb2-8ead-0b5d81431d36', 1, 4, 0, 0, 22500, '2025-03-20 18:23:54', null, null, null, '1', '2025-03-20 18:23:54', '0', '2025-03-20 19:31:17', false),
        (7, '2320d8b5-f67c-459c-9ff1-1a4f73821592', 1, 4, 0, 0, 45500, '2025-03-20 20:12:39', null, null, null, '1', '2025-03-20 20:12:39', '0', '2025-03-20 21:12:05', false),
        (8, 'cde1cece-ce5e-49d1-892b-d5f40e7fbaa3', 1, 3, 47001, 2000000, 47000, '2025-03-20 21:32:14', '2025-03-20 21:32:30', 'org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insert (batch index #2) failed. 1 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Duplicate entry ''47182'' for key ''udx_mid_stdlib_id''
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insert (batch index #2) failed. 1 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Duplicate entry ''47182'' for key ''udx_mid_stdlib_id''
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:254)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:149)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:130)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:118)
	at com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils.execute(MybatisBatchUtils.java:175)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.insert(BaseMapper.java:485)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy3/jdk.proxy3.$Proxy312.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.toolkit.Db.lambda$saveBatch$223719e8$1(Db.java:90)
	at com.baomidou.mybatisplus.extension.toolkit.SqlHelper.execute(SqlHelper.java:339)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:90)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:76)
	at cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX.insertBatch(BaseMapperX.java:153)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy3/jdk.proxy3.$Proxy312.insertBatch(Unknown Source)
	at com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.lambda$insertOrUpdateWithCheckUnique$4(ProductStdlibMapper.java:183)
	at java.base/java.util.HashMap.forEach(HashMap.java:1429)
	at com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insertOrUpdateWithCheckUnique(ProductStdlibMapper.java:181)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$D', null, '1', '2025-03-20 21:32:14', '0', '2025-03-20 21:32:14', false),
        (9, 'f11840ba-2878-40f0-9517-b4be1b5a66f4', 1, 4, 47001, 2000000, 84000, '2025-03-20 22:16:24', null, null, null, '1', '2025-03-20 22:16:24', '0', '2025-03-20 22:24:13', false),
        (10, 'd1dc98e4-55db-4a84-a66c-f625d1940dab', 1, 2, 47001, 2000000, 2000000, '2025-03-20 22:24:28', '2025-03-21 12:59:36', null, null, '1', '2025-03-20 22:24:28', '0', '2025-03-21 12:50:57', false),
        (11, '5e43cf06-545e-4ac4-bcb9-f35a9e34c338', 1, 2, 1, 8000000, 8000500, '2025-03-28 17:29:40', '2025-04-01 20:18:50', 'org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insert (batch index #25) failed. 24 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Data truncation: Data too long for column ''outer_package_images'' at row 1
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insert (batch index #25) failed. 24 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Data truncation: Data too long for column ''outer_package_images'' at row 1
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:254)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:149)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:130)
	at com.baomidou.mybatisplus.core.batch.MybatisBatch.execute(MybatisBatch.java:118)
	at com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils.execute(MybatisBatchUtils.java:175)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.insert(BaseMapper.java:485)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy313.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.toolkit.Db.lambda$saveBatch$223719e8$1(Db.java:90)
	at com.baomidou.mybatisplus.extension.toolkit.SqlHelper.execute(SqlHelper.java:339)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:90)
	at com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch(Db.java:76)
	at cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX.insertBatch(BaseMapperX.java:153)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy313.insertBatch(Unknown Source)
	at com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.lambda$insertOrUpdateWithCheckUnique$4(ProductStdlibMapper.java:189)
	at java.base/java.util.HashMap.forEach(HashMap.java:1429)
	at com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper.insertOrUpdateWithCheckUnique(ProductStdlibMapper.java:187)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:733)
	at com.baomido', null, '1', '2025-03-28 17:29:40', '0', '2025-04-01 20:18:50', false),
        (12, '55c5760a-b708-415a-99b9-4ebd78a27c13', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 09:37:23', '2025-04-03 09:37:24', '中台商品不存在或跳过更新', null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (13, 'df0538fd-33bc-4b5b-a0ba-8f21e81f3c45', 2, 2, 874120, 874120, 874120, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (14, '3e35f790-8aff-47d7-9b9d-23b06f38fb8e', 2, 2, 391, 391, 391, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (15, '8c822b68-edc2-441d-918f-58dfd406d13c', 2, 2, 312, 312, 312, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (16, '8b4573e7-4df9-4784-8d97-06f65ea3a1da', 2, 2, 60, 60, 60, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (17, 'ba2aa29d-e795-4e7e-85f0-b1295cdebd11', 2, 2, 257, 257, 257, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (18, '72e9da9a-7cc3-4566-8ee2-8dc7f916c2a4', 2, 2, 182, 182, 182, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (19, '0669ff07-4cf6-4d8b-8208-fb34b41b1950', 2, 2, 420, 420, 420, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (20, '05d2ac63-e915-4f39-9b24-524014f827bc', 2, 2, 252, 252, 252, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (21, 'ca8c73ef-18fb-49c5-a255-eec80dfe5a8f', 2, 2, 285, 285, 285, '2025-04-03 09:37:23', '2025-04-03 09:37:24', null, null, '1', '2025-04-03 09:37:23', '0', '2025-04-03 09:37:23', false),
        (22, '8c940ba9-5018-4637-9eeb-903718cd678b', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 09:41:07', '2025-04-03 09:41:08', '中台商品不存在或跳过更新', null, '1', '2025-04-03 09:41:07', '0', '2025-04-03 09:41:07', false),
        (23, '6eda6a67-5943-4af0-8a78-a604c6ebeac3', 2, 2, 874120, 874120, 874120, '2025-04-03 09:41:07', '2025-04-03 09:41:08', null, null, '1', '2025-04-03 09:41:07', '0', '2025-04-03 09:41:07', false),
        (24, 'b4618686-fd9a-452f-afb1-e6232a7f6a6f', 2, 2, 391, 391, 391, '2025-04-03 09:41:07', '2025-04-03 09:41:08', null, null, '1', '2025-04-03 09:41:07', '0', '2025-04-03 09:41:07', false),
        (25, '9b522925-a301-43fc-b379-a4b37275d3cc', 2, 2, 312, 312, 312, '2025-04-03 09:41:07', '2025-04-03 09:41:08', null, null, '1', '2025-04-03 09:41:07', '0', '2025-04-03 09:41:07', false),
        (26, '0152db97-4679-4660-9235-e18e38cf57b8', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 09:41:58', '2025-04-03 09:41:58', '中台商品不存在或跳过更新', null, '1', '2025-04-03 09:41:58', '0', '2025-04-03 09:41:58', false),
        (27, '23882cad-7379-4388-a973-acbe9e2561e1', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 09:42:01', '2025-04-03 09:42:02', '中台商品不存在或跳过更新', null, '1', '2025-04-03 09:42:01', '0', '2025-04-03 09:42:01', false),
        (28, '5c8aed8b-11a8-4c5c-b43f-033c249737fe', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 10:31:12', '2025-04-03 10:31:13', '中台商品不存在或跳过更新', null, '1', '2025-04-03 10:31:12', '0', '2025-04-03 10:31:12', false),
        (29, '5cdb7a1e-bd7c-4063-b437-2017e8fdcb0b', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 10:31:16', '2025-04-03 10:31:17', '中台商品不存在或跳过更新', null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (30, 'fab438b4-c4fb-441b-81fe-bdec3a19a980', 2, 2, 874120, 874120, 874120, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (31, '50d37b97-8290-4ff9-8f4d-034e6b8a5182', 2, 2, 391, 391, 391, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (32, '81780888-7c63-404e-8433-f1346469d1e9', 2, 2, 312, 312, 312, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (33, 'ddf14ac8-bce5-4a9c-8d45-f85c6089daa1', 2, 2, 60, 60, 60, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (34, 'ddba83d7-65d6-4a94-995b-c2390db0e1f9', 2, 2, 257, 257, 257, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (35, '528f63fc-ac28-4dc1-9ded-3469905bb883', 2, 2, 182, 182, 182, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (36, '4206e6e0-36de-423d-b4fb-8cb058a19209', 2, 2, 420, 420, 420, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (37, '66234eb0-f123-4cd8-aa35-548cc0946892', 2, 2, 252, 252, 252, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (38, 'b78fd686-3b92-4514-a121-74e9c9b50b93', 2, 2, 285, 285, 285, '2025-04-03 10:31:16', '2025-04-03 10:31:17', null, null, '1', '2025-04-03 10:31:16', '0', '2025-04-03 10:31:16', false),
        (39, 'c8568e59-8abc-4a96-a168-45a497090df5', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 14:01:15', '2025-04-03 14:01:16', '中台商品不存在或跳过更新', null, '1', '2025-04-03 14:01:15', '0', '2025-04-03 14:01:15', false),
        (40, '68f4bd75-d4e4-4dc3-b00f-8dd380ea9b84', 2, 3, 874120, 874120, 874120, '2025-04-03 14:01:15', '2025-04-03 14:01:16', '中台商品不存在或跳过更新', null, '1', '2025-04-03 14:01:15', '0', '2025-04-03 14:01:15', false),
        (41, '8a6a92fa-9e5f-45f6-9821-e6b751e17af6', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 17:18:09', '2025-04-03 17:18:10', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (42, 'b151bc30-2e66-4a86-9b99-ac04bc650683', 2, 3, 874120, 874120, 874120, '2025-04-03 17:18:09', '2025-04-03 17:18:10', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (43, '939c59c9-3332-48b4-86f5-82626a57de5b', 2, 3, 391, 391, 391, '2025-04-03 17:18:09', '2025-04-03 17:18:10', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (44, '7422dff0-88be-48a4-add7-c3a12abbeca2', 2, 3, 312, 312, 312, '2025-04-03 17:18:09', '2025-04-03 17:18:10', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (45, '4b82109c-f126-4270-85d3-e608ba467d47', 2, 2, 60, 60, 60, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (46, '65c9ac2a-e89a-4335-8643-2fea52fa2c17', 2, 2, 257, 257, 257, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (47, '3eab6c81-f111-415b-9b39-39caf92392a4', 2, 2, 182, 182, 182, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (48, '8e540a41-595b-4ec3-bccd-746fd4f14c13', 2, 2, 420, 420, 420, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (49, '04c42224-f919-4cca-86d4-e15e05bbc8a3', 2, 2, 252, 252, 252, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (50, '0ed3d46a-e904-4658-a7f8-a6c313d630c1', 2, 2, 285, 285, 285, '2025-04-03 17:18:09', '2025-04-03 17:18:10', null, null, '1', '2025-04-03 17:18:09', '0', '2025-04-03 17:18:09', false),
        (51, 'e3d82398-cd9f-4585-8cc6-046f48b89f75', 2, 3, 6926033, 6926033, 6926033, '2025-04-03 17:18:11', '2025-04-03 17:18:12', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (52, 'd744eb22-c5c6-457e-ab7a-8e11a633a5ff', 2, 3, 874120, 874120, 874120, '2025-04-03 17:18:11', '2025-04-03 17:18:12', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (53, '28c6ce2e-2021-4ec4-8893-e7957d45c710', 2, 3, 391, 391, 391, '2025-04-03 17:18:11', '2025-04-03 17:18:12', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (54, '85098ff3-c851-458b-8d3d-9b9122a63d89', 2, 3, 312, 312, 312, '2025-04-03 17:18:11', '2025-04-03 17:18:12', '中台商品不存在或跳过更新', null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (55, '1240f000-69ae-42ad-8bf9-0503a3cfcace', 2, 2, 60, 60, 60, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (56, '18ee1c60-d2f9-4505-ad04-509f820fce28', 2, 2, 257, 257, 257, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (57, '8ad71d6c-9885-4e50-8e77-fde5fb13e07c', 2, 2, 182, 182, 182, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (58, '05092c58-00b8-4cde-80ef-fcf9937bffac', 2, 2, 420, 420, 420, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (59, '001d567f-0324-4ea0-9aa7-190dfdc2bffe', 2, 2, 252, 252, 252, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (60, '0c5f4150-784a-411d-b7aa-dd3f58575d49', 2, 2, 285, 285, 285, '2025-04-03 17:18:11', '2025-04-03 17:18:12', null, null, '1', '2025-04-03 17:18:11', '0', '2025-04-03 17:18:11', false),
        (61, '454d9689-141a-41e2-a93b-3faaa73ac0b8', 2, 2, 6926033, 6926033, 6926033, '2025-04-07 10:59:08', '2025-04-07 10:59:09', null, null, '1', '2025-04-07 10:59:08', '0', '2025-04-07 10:59:08', false),
        (62, '39e9262b-22d7-464f-aea9-8bb5ad3877e8', 2, 2, 6926033, 6926033, 6926033, '2025-04-07 10:59:09', '2025-04-07 10:59:10', null, null, '1', '2025-04-07 10:59:09', '0', '2025-04-07 10:59:09', false),
        (63, '719cbb3e-ae4e-48b8-b655-6c427ef724bd', 2, 2, 6926033, 6926033, 6926033, '2025-04-08 09:59:21', '2025-04-08 09:59:22', null, null, '1', '2025-04-08 09:59:21', '0', '2025-04-08 09:59:21', false),
        (64, '94af315e-3d1c-4439-9ff6-301c93fe0c46', 2, 2, 6926033, 6926033, 6926033, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (65, 'c9275e79-77d6-47ed-9408-308efbc8ecc9', 2, 2, 874120, 874120, 874120, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (66, '9940e34e-a210-423d-853e-aae4ff28311b', 2, 2, 391, 391, 391, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (67, '03e88832-7d98-4ae6-8e13-17eedc0432b4', 2, 3, 312, 312, 312, '2025-04-08 11:15:36', '2025-04-08 11:15:37', '中台商品不存在或跳过更新', null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (68, '013314e2-726f-4545-a817-104a0ed0e555', 2, 2, 60, 60, 60, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (69, '41179370-a32d-498d-8644-9deb2ab1d441', 2, 2, 257, 257, 257, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (70, '57c61b4e-6546-4297-a479-289895fa44b0', 2, 2, 182, 182, 182, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (71, '76882c43-388f-4b91-b341-86e11c0880e2', 2, 2, 420, 420, 420, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (72, '0fcf5d15-7c1d-4f2d-8346-10714d958ca0', 2, 2, 252, 252, 252, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (73, '3ff569a0-259d-4757-b618-a01c6c84c68c', 2, 2, 285, 285, 285, '2025-04-08 11:15:36', '2025-04-08 11:15:37', null, null, '1', '2025-04-08 11:15:36', '0', '2025-04-08 11:15:36', false),
        (74, '5d4e96a1-9f78-4c8f-9793-4dc1f5916176', 2, 2, 6926033, 6926033, 6926033, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (75, '5e003da3-8846-4cb8-9d29-aa57dca7d085', 2, 2, 874120, 874120, 874120, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (76, 'e50265bc-78c5-4018-af69-05be1ff2e947', 2, 2, 391, 391, 391, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (77, '6b083876-6a73-4052-acd6-829309e12dbc', 2, 3, 312, 312, 312, '2025-04-08 11:17:34', '2025-04-08 11:17:35', '中台商品不存在或跳过更新', null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (78, '43b5db17-b76e-4285-bf66-ef9b8e45d387', 2, 2, 60, 60, 60, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (79, '21b88fe3-d9a8-4968-b075-d60d80c1ecaa', 2, 2, 257, 257, 257, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (80, '8b69e94c-d242-4cce-ad8a-78d486e2d674', 2, 2, 182, 182, 182, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (81, '495c2220-9e38-454c-a742-d32c5338ef25', 2, 2, 420, 420, 420, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (82, '93d75914-0707-4c44-b49c-2141820a67af', 2, 2, 252, 252, 252, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (83, 'a18500ca-9897-43cc-aee5-3c93208cf9a7', 2, 2, 285, 285, 285, '2025-04-08 11:17:34', '2025-04-08 11:17:35', null, null, '1', '2025-04-08 11:17:34', '0', '2025-04-08 11:17:34', false),
        (84, '2748d12c-785c-49a1-91c5-ed85a7d98773', 2, 2, 6926033, 6926033, 6926033, '2025-04-08 11:22:37', '2025-04-08 11:22:38', null, null, '1', '2025-04-08 11:22:37', '0', '2025-04-08 11:22:37', false),
        (85, '8dfd01c7-d5d7-42ab-851b-15653f882f6c', 2, 2, 130875, 130875, 130875, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (86, 'eb7b5d6f-2f20-4b4d-aa53-ac2c78458ae7', 2, 2, 162859, 162859, 162859, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (87, 'af8e592b-52f3-49ae-aeb0-2e42f9411e70', 2, 2, 6944304, 6944304, 6944304, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (88, 'ce173900-1863-4902-9552-8903a3762f4f', 2, 2, 140, 140, 140, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (89, 'b54d03d0-4b3a-410c-924b-6ca014a48684', 2, 2, 174129, 174129, 174129, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (90, 'b70ec185-4ee7-4da3-b373-fbc05ecb13f8', 2, 2, 112094, 112094, 112094, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (91, '34935775-bac9-4cc6-b2ee-4658f03648bd', 2, 2, 189058, 189058, 189058, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (92, '66ac2a7d-2eea-4b4a-865a-ce4efaead06f', 2, 2, 76946, 76946, 76946, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (93, '76b89cfe-574b-4378-b2db-c5a8f6b0abe9', 2, 2, 112615, 112615, 112615, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (94, '4b7b51eb-696c-47f3-893f-19ec680ddbdb', 2, 2, 122054, 122054, 122054, '2025-04-08 11:44:31', '2025-04-08 11:44:34', null, null, '1', '2025-04-08 11:44:31', '0', '2025-04-08 11:44:31', false),
        (95, 'a8c2c1ab-d852-4ea3-bd36-cbe1fb392229', 2, 2, 130875, 130875, 130875, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (96, '859d27ad-c676-4e4f-a055-a3993b8a81d7', 2, 2, 162859, 162859, 162859, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (97, '5a045834-a81a-49cf-880f-738f51c8ee05', 2, 2, 6944304, 6944304, 6944304, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (98, 'b9678d84-aff9-42c9-979c-eb85a1969850', 2, 2, 140, 140, 140, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (99, 'dcbfd7db-5d2d-47e7-a578-c8d19ca0a0d2', 2, 2, 174129, 174129, 174129, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (100, '66c9bc7c-796c-4e69-9007-fc3590bec8e7', 2, 2, 112094, 112094, 112094, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (101, '50a04959-7a67-4c00-a46c-3ebe917f99d7', 2, 2, 189058, 189058, 189058, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (102, 'b412ae71-38e5-4b7e-af99-87465f254bec', 2, 2, 76946, 76946, 76946, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (103, 'a2f84239-c062-467d-819f-3b73c6feb9f7', 2, 2, 112615, 112615, 112615, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (104, 'e2d67263-6490-4022-a4b2-22485fb2ad66', 2, 2, 122054, 122054, 122054, '2025-04-08 11:44:50', '2025-04-08 11:44:51', null, null, '1', '2025-04-08 11:44:50', '0', '2025-04-08 11:44:50', false),
        (105, 'ba640e13-edcc-4b51-9a22-a9e09bfb286f', 2, 2, 130875, 130875, 130875, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (106, '3644c802-ecca-4f69-b799-31e61c601990', 2, 2, 162859, 162859, 162859, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (107, '937f233f-31db-4e2c-8132-c9165b30c399', 2, 2, 6944304, 6944304, 6944304, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (108, '59b8ad18-a7ef-4ac7-a76f-56ead92dfcdf', 2, 2, 140, 140, 140, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (109, '2160f1f9-1cde-4209-a5f0-8c5215cd29e1', 2, 2, 174129, 174129, 174129, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (110, '309a76ec-d866-421d-8722-86c7bc1e99e0', 2, 2, 112094, 112094, 112094, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (111, '345f1e8c-116d-435b-912c-f83e9241e3a3', 2, 2, 189058, 189058, 189058, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (112, 'b337efb0-d4e2-431c-b1be-2418505251a5', 2, 2, 76946, 76946, 76946, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (113, '14b1ad15-276a-4832-b262-cde63dc3e5ac', 2, 2, 112615, 112615, 112615, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (114, 'dfa74249-0f25-4ce5-89a1-9bae4de17732', 2, 2, 122054, 122054, 122054, '2025-04-08 11:45:04', '2025-04-08 11:45:06', null, null, '1', '2025-04-08 11:45:04', '0', '2025-04-08 11:45:04', false),
        (115, '7bde4b9a-12dd-4f81-949e-4e1ed8952976', 2, 2, 6926033, 6926033, 6926033, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (116, 'dbe29be9-5979-465f-a20f-4cf1e175d005', 2, 2, 874120, 874120, 874120, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (117, '37d92a5e-c389-442d-a065-31e5a69bf9d9', 2, 2, 391, 391, 391, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (118, 'e16f3a8b-e877-4d64-8055-2212cdeee4fd', 2, 3, 312, 312, 312, '2025-04-08 12:02:55', '2025-04-08 12:02:56', '中台商品不存在或跳过更新', null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (119, 'b0639c37-6f2c-4478-987e-d1d7565c0027', 2, 2, 60, 60, 60, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (120, '58b53548-827c-4318-a0b5-0024903baef0', 2, 2, 257, 257, 257, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (121, 'f3b12274-4647-43e6-883b-e2ec245778a3', 2, 2, 182, 182, 182, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (122, 'cd6c14f8-aa46-4a10-9c84-b7ebfcace1ec', 2, 2, 420, 420, 420, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (123, '592248c7-9106-4412-bb1a-0495d27b1425', 2, 2, 252, 252, 252, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (124, '11c98171-6c60-412a-ba15-fdf6283a6226', 2, 2, 285, 285, 285, '2025-04-08 12:02:55', '2025-04-08 12:02:56', null, null, '1', '2025-04-08 12:02:55', '0', '2025-04-08 12:02:55', false),
        (125, '46a83d4f-03db-4ef1-81c0-3a0e63dcf364', 2, 2, 874120, 874120, 874120, '2025-04-08 13:26:38', '2025-04-08 13:26:38', null, null, '1', '2025-04-08 13:26:38', '0', '2025-04-08 13:26:38', false),
        (126, '422f985b-79db-4660-84ca-254d952db35d', 2, 2, 874800, 874800, 874800, '2025-04-21 14:49:51', '2025-04-21 14:49:52', null, null, '1', '2025-04-21 14:49:51', '0', '2025-04-21 14:49:51', false),
        (127, '4b8c7e5a-8dc1-4c7f-ad37-d2ec52363c0e', 2, 2, 874810, 874810, 874810, '2025-04-21 16:34:23', '2025-04-21 16:34:24', null, null, '1', '2025-04-21 16:34:23', '0', '2025-04-21 16:34:23', false),
        (128, '13e30f93-a383-4c15-90ae-7cd57898a912', 1, 2, 10000, 50000, 50000, '2025-04-22 20:19:59', '2025-04-22 21:04:19', null, null, '1', '2025-04-22 20:19:59', '0', '2025-04-22 21:04:19', false),
        (129, 'd36ee1f4-f22b-4fc1-a74b-9b94e31cff3d', 2, 2, 874826, 874826, 874826, '2025-04-22 20:22:02', '2025-04-22 20:22:39', null, null, '1', '2025-04-22 20:22:02', '0', '2025-04-22 20:22:02', false),
        (131, '0de2ae72-4951-4367-9407-3959958f8377', 2, 2, 874836, 874836, 874836, '2025-04-23 15:07:15', '2025-04-23 15:08:12', null, null, '1', '2025-04-23 15:07:15', '0', '2025-04-23 15:07:15', false),
        (132, '01f73a2b-489a-4d1f-b440-d1de736f0d45', 2, 2, 874535, 874535, 874535, '2025-05-13 09:53:42', '2025-05-13 09:53:43', null, null, '1', '2025-05-13 09:53:42', '0', '2025-05-13 09:53:42', false);