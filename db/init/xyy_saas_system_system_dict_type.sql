insert into xyy_saas_system.system_dict_type (id, name, type, status, remark, creator, create_time, updater, update_time, deleted, deleted_time)
values  (1, '用户性别', 'system_user_sex', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2022-05-16 20:29:32', false, null),
        (6, '参数类型', 'infra_config_type', 0, null, 'admin', '2021-01-05 17:03:48', '', '2022-02-01 16:36:54', false, null),
        (7, '通知类型', 'system_notice_type', 0, null, 'admin', '2021-01-05 17:03:48', '', '2022-02-01 16:35:26', false, null),
        (9, '操作类型', 'infra_operate_type', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2024-03-14 12:44:01', false, null),
        (10, '系统状态', 'common_status', 0, null, 'admin', '2021-01-05 17:03:48', '', '2022-02-01 16:21:28', false, null),
        (11, 'Boolean 是否类型', 'infra_boolean_string', 0, 'boolean 转是否', '', '2021-01-19 03:20:08', '', '2022-02-01 16:37:10', false, null),
        (104, '登陆结果', 'system_login_result', 0, '登陆结果', '', '2021-01-18 06:17:11', '', '2022-02-01 16:36:00', false, null),
        (106, '代码生成模板类型', 'infra_codegen_template_type', 0, null, '', '2021-02-05 07:08:06', '1', '2022-05-16 20:26:50', false, null),
        (107, '定时任务状态', 'infra_job_status', 0, null, '', '2021-02-07 07:44:16', '', '2022-02-01 16:51:11', false, null),
        (108, '定时任务日志状态', 'infra_job_log_status', 0, null, '', '2021-02-08 10:03:51', '', '2022-02-01 16:50:43', false, null),
        (109, '用户类型', 'user_type', 0, null, '', '2021-02-26 00:15:51', '', '2021-02-26 00:15:51', false, null),
        (110, 'API 异常数据的处理状态', 'infra_api_error_log_process_status', 0, null, '', '2021-02-26 07:07:01', '', '2022-02-01 16:50:53', false, null),
        (111, '短信渠道编码', 'system_sms_channel_code', 0, null, '1', '2021-04-05 01:04:50', '1', '2022-02-16 02:09:08', false, null),
        (112, '短信模板的类型', 'system_sms_template_type', 0, null, '1', '2021-04-05 21:50:43', '1', '2022-02-01 16:35:06', false, null),
        (113, '短信发送状态', 'system_sms_send_status', 0, null, '1', '2021-04-11 20:18:03', '1', '2022-02-01 16:35:09', false, null),
        (114, '短信接收状态', 'system_sms_receive_status', 0, null, '1', '2021-04-11 20:27:14', '1', '2022-02-01 16:35:14', false, null),
        (116, '登陆日志的类型', 'system_login_type', 0, '登陆日志的类型', '1', '2021-10-06 00:50:46', '1', '2022-02-01 16:35:56', false, null),
        (117, 'OA 请假类型', 'bpm_oa_leave_type', 0, null, '1', '2021-09-21 22:34:33', '1', '2022-01-22 10:41:37', false, null),
        (130, '支付渠道编码类型', 'pay_channel_code', 0, '支付渠道的编码', '1', '2021-12-03 10:35:08', '1', '2023-07-10 10:11:39', false, null),
        (131, '支付回调状态', 'pay_notify_status', 0, '支付回调状态（包括退款回调）', '1', '2021-12-03 10:53:29', '1', '2023-07-19 18:09:43', false, null),
        (132, '支付订单状态', 'pay_order_status', 0, '支付订单状态', '1', '2021-12-03 11:17:50', '1', '2021-12-03 11:17:50', false, null),
        (134, '退款订单状态', 'pay_refund_status', 0, '退款订单状态', '1', '2021-12-10 16:42:50', '1', '2023-07-19 10:13:17', false, null),
        (139, '流程实例的状态', 'bpm_process_instance_status', 0, '流程实例的状态', '1', '2022-01-07 23:46:42', '1', '2022-01-07 23:46:42', false, null),
        (140, '流程实例的结果', 'bpm_task_status', 0, '流程实例的结果', '1', '2022-01-07 23:48:10', '1', '2024-03-08 22:42:03', false, null),
        (141, '流程的表单类型', 'bpm_model_form_type', 0, '流程的表单类型', '103', '2022-01-11 23:50:45', '103', '2022-01-11 23:50:45', false, null),
        (142, '任务分配规则的类型', 'bpm_task_candidate_strategy', 0, 'BPM 任务的候选人的策略', '103', '2022-01-12 23:21:04', '103', '2024-03-06 02:53:59', false, null),
        (144, '代码生成的场景枚举', 'infra_codegen_scene', 0, '代码生成的场景枚举', '1', '2022-02-02 13:14:45', '1', '2022-03-10 16:33:46', false, null),
        (145, '角色类型', 'system_role_type', 0, '角色类型', '1', '2022-02-16 13:01:46', '1', '2022-02-16 13:01:46', false, null),
        (146, '文件存储器', 'infra_file_storage', 0, '文件存储器', '1', '2022-03-15 00:24:38', '1', '2022-03-15 00:24:38', false, null),
        (147, 'OAuth 2.0 授权类型', 'system_oauth2_grant_type', 0, 'OAuth 2.0 授权类型（模式）', '1', '2022-05-12 00:20:52', '1', '2022-05-11 16:25:49', false, null),
        (149, '商品 SPU 状态', 'product_spu_status', 0, '商品 SPU 状态', '1', '2022-10-24 21:19:04', '1', '2022-10-24 21:19:08', false, null),
        (150, '优惠类型', 'promotion_discount_type', 0, '优惠类型', '1', '2022-11-01 12:46:06', '1', '2022-11-01 12:46:06', false, null),
        (151, '优惠劵模板的有限期类型', 'promotion_coupon_template_validity_type', 0, '优惠劵模板的有限期类型', '1', '2022-11-02 00:06:20', '1', '2022-11-04 00:08:26', false, null),
        (152, '营销的商品范围', 'promotion_product_scope', 0, '营销的商品范围', '1', '2022-11-02 00:28:01', '1', '2022-11-02 00:28:01', false, null),
        (153, '优惠劵的状态', 'promotion_coupon_status', 0, '优惠劵的状态', '1', '2022-11-04 00:14:49', '1', '2022-11-04 00:14:49', false, null),
        (154, '优惠劵的领取方式', 'promotion_coupon_take_type', 0, '优惠劵的领取方式', '1', '2022-11-04 19:12:27', '1', '2022-11-04 19:12:27', false, null),
        (155, '促销活动的状态', 'promotion_activity_status', 0, '促销活动的状态', '1', '2022-11-04 22:54:23', '1', '2022-11-04 22:54:23', false, null),
        (156, '营销的条件类型', 'promotion_condition_type', 0, '营销的条件类型', '1', '2022-11-04 22:59:23', '1', '2022-11-04 22:59:23', false, null),
        (157, '交易售后状态', 'trade_after_sale_status', 0, '交易售后状态', '1', '2022-11-19 20:52:56', '1', '2022-11-19 20:52:56', false, null),
        (158, '交易售后的类型', 'trade_after_sale_type', 0, '交易售后的类型', '1', '2022-11-19 21:04:09', '1', '2022-11-19 21:04:09', false, null),
        (159, '交易售后的方式', 'trade_after_sale_way', 0, '交易售后的方式', '1', '2022-11-19 21:39:04', '1', '2022-11-19 21:39:04', false, null),
        (160, '终端', 'terminal', 0, '终端', '1', '2022-12-10 10:50:50', '1', '2022-12-10 10:53:11', false, null),
        (161, '交易订单的类型', 'trade_order_type', 0, '交易订单的类型', '1', '2022-12-10 16:33:54', '1', '2022-12-10 16:33:54', false, null),
        (162, '交易订单的状态', 'trade_order_status', 0, '交易订单的状态', '1', '2022-12-10 16:48:44', '1', '2022-12-10 16:48:44', false, null),
        (163, '交易订单项的售后状态', 'trade_order_item_after_sale_status', 0, '交易订单项的售后状态', '1', '2022-12-10 20:58:08', '1', '2022-12-10 20:58:08', false, null),
        (164, '公众号自动回复的请求关键字匹配模式', 'mp_auto_reply_request_match', 0, '公众号自动回复的请求关键字匹配模式', '1', '2023-01-16 23:29:56', '1', '2023-01-16 23:29:56', false, '1970-01-01 00:00:00'),
        (165, '公众号的消息类型', 'mp_message_type', 0, '公众号的消息类型', '1', '2023-01-17 22:17:09', '1', '2023-01-17 22:17:09', false, '1970-01-01 00:00:00'),
        (166, '邮件发送状态', 'system_mail_send_status', 0, '邮件发送状态', '1', '2023-01-26 09:53:13', '1', '2023-01-26 09:53:13', false, '1970-01-01 00:00:00'),
        (167, '站内信模版的类型', 'system_notify_template_type', 0, '站内信模版的类型', '1', '2023-01-28 10:35:10', '1', '2023-01-28 10:35:10', false, '1970-01-01 00:00:00'),
        (168, '代码生成的前端类型', 'infra_codegen_front_type', 0, '', '1', '2023-04-12 23:57:52', '1', '2023-04-12 23:57:52', false, '1970-01-01 00:00:00'),
        (170, '快递计费方式', 'trade_delivery_express_charge_mode', 0, '用于商城交易模块配送管理', '1', '2023-05-21 22:45:03', '1', '2023-05-21 22:45:03', false, '1970-01-01 00:00:00'),
        (171, '积分业务类型', 'member_point_biz_type', 0, '', '1', '2023-06-10 12:15:00', '1', '2023-06-28 13:48:20', false, '1970-01-01 00:00:00'),
        (173, '支付通知类型', 'pay_notify_type', 0, null, '1', '2023-07-20 12:23:03', '1', '2023-07-20 12:23:03', false, '1970-01-01 00:00:00'),
        (174, '会员经验业务类型', 'member_experience_biz_type', 0, null, '', '2023-08-22 12:41:01', '', '2023-08-22 12:41:01', false, null),
        (175, '交易配送类型', 'trade_delivery_type', 0, '', '1', '2023-08-23 00:03:14', '1', '2023-08-23 00:03:14', false, '1970-01-01 00:00:00'),
        (176, '分佣模式', 'brokerage_enabled_condition', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (177, '分销关系绑定模式', 'brokerage_bind_mode', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (178, '佣金提现类型', 'brokerage_withdraw_type', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (179, '佣金记录业务类型', 'brokerage_record_biz_type', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (180, '佣金记录状态', 'brokerage_record_status', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (181, '佣金提现状态', 'brokerage_withdraw_status', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (182, '佣金提现银行', 'brokerage_bank_name', 0, null, '', '2023-09-28 02:46:05', '', '2023-09-28 02:46:05', false, null),
        (183, '砍价记录的状态', 'promotion_bargain_record_status', 0, '', '1', '2023-10-05 10:41:08', '1', '2023-10-05 10:41:08', false, '1970-01-01 00:00:00'),
        (184, '拼团记录的状态', 'promotion_combination_record_status', 0, '', '1', '2023-10-08 07:24:25', '1', '2023-10-08 07:24:25', false, '1970-01-01 00:00:00'),
        (185, '回款-回款方式', 'crm_receivable_return_type', 0, '回款-回款方式', '1', '2023-10-18 21:54:10', '1', '2023-10-18 21:54:10', false, '1970-01-01 00:00:00'),
        (186, 'CRM 客户行业', 'crm_customer_industry', 0, 'CRM 客户所属行业', '1', '2023-10-28 22:57:07', '1', '2024-02-18 23:30:22', false, null),
        (187, '客户等级', 'crm_customer_level', 0, 'CRM 客户等级', '1', '2023-10-28 22:59:12', '1', '2023-10-28 15:11:16', false, null),
        (188, '客户来源', 'crm_customer_source', 0, 'CRM 客户来源', '1', '2023-10-28 23:00:34', '1', '2023-10-28 15:11:16', false, null),
        (600, 'Banner 位置', 'promotion_banner_position', 0, '', '1', '2023-10-08 07:24:25', '1', '2023-11-04 13:04:02', false, '1970-01-01 00:00:00'),
        (601, '社交类型', 'system_social_type', 0, '', '1', '2023-11-04 13:03:54', '1', '2023-11-04 13:03:54', false, '1970-01-01 00:00:00'),
        (604, '产品状态', 'crm_product_status', 0, '', '1', '2023-10-30 21:47:59', '1', '2023-10-30 21:48:45', false, '1970-01-01 00:00:00'),
        (605, 'CRM 数据权限的级别', 'crm_permission_level', 0, '', '1', '2023-11-30 09:51:59', '1', '2023-11-30 09:51:59', false, '1970-01-01 00:00:00'),
        (606, 'CRM 审批状态', 'crm_audit_status', 0, '', '1', '2023-11-30 18:56:23', '1', '2023-11-30 18:56:23', false, '1970-01-01 00:00:00'),
        (607, 'CRM 产品单位', 'crm_product_unit', 0, '', '1', '2023-12-05 23:01:51', '1', '2023-12-05 23:01:51', false, '1970-01-01 00:00:00'),
        (608, 'CRM 跟进方式', 'crm_follow_up_type', 0, '', '1', '2024-01-15 20:48:05', '1', '2024-01-15 20:48:05', false, '1970-01-01 00:00:00'),
        (609, '支付转账类型', 'pay_transfer_type', 0, '', '1', '2023-10-28 16:27:18', '1', '2023-10-28 16:27:18', false, '1970-01-01 00:00:00'),
        (610, '转账订单状态', 'pay_transfer_status', 0, '', '1', '2023-10-28 16:18:32', '1', '2023-10-28 16:18:32', false, '1970-01-01 00:00:00'),
        (611, 'ERP 库存明细的业务类型', 'erp_stock_record_biz_type', 0, 'ERP 库存明细的业务类型', '1', '2024-02-05 18:07:02', '1', '2024-02-05 18:07:02', false, '1970-01-01 00:00:00'),
        (612, 'ERP 审批状态', 'erp_audit_status', 0, '', '1', '2024-02-06 00:00:07', '1', '2024-02-06 00:00:07', false, '1970-01-01 00:00:00'),
        (613, 'BPM 监听器类型', 'bpm_process_listener_type', 0, '', '1', '2024-03-23 12:52:24', '1', '2024-03-09 15:54:28', false, '1970-01-01 00:00:00'),
        (615, 'BPM 监听器值类型', 'bpm_process_listener_value_type', 0, '', '1', '2024-03-23 13:00:31', '1', '2024-03-23 13:00:31', false, '1970-01-01 00:00:00'),
        (616, '时间间隔', 'date_interval', 0, '', '1', '2024-03-29 22:50:09', '1', '2024-03-29 22:50:09', false, '1970-01-01 00:00:00'),
        (619, 'CRM 商机结束状态类型', 'crm_business_end_status_type', 0, '', '1', '2024-04-13 23:23:00', '1', '2024-04-13 23:23:00', false, '1970-01-01 00:00:00'),
        (620, 'AI 模型平台', 'ai_platform', 0, '', '1', '2024-05-09 22:27:38', '1', '2024-05-09 22:27:38', false, '1970-01-01 00:00:00'),
        (621, 'AI 绘画状态', 'ai_image_status', 0, '', '1', '2024-06-26 20:51:23', '1', '2024-06-26 20:51:23', false, '1970-01-01 00:00:00'),
        (622, 'AI 音乐状态', 'ai_music_status', 0, '', '1', '2024-06-27 22:45:07', '1', '2024-06-28 00:56:27', false, '1970-01-01 00:00:00'),
        (623, 'AI 音乐生成模式', 'ai_generate_mode', 0, '', '1', '2024-06-27 22:46:21', '1', '2024-06-28 01:22:29', false, '1970-01-01 00:00:00'),
        (624, '写作语气', 'ai_write_tone', 0, '', '1', '2024-07-07 15:19:02', '1', '2024-07-07 15:19:02', false, '1970-01-01 00:00:00'),
        (625, '写作语言', 'ai_write_language', 0, '', '1', '2024-07-07 15:18:52', '1', '2024-07-07 15:18:52', false, '1970-01-01 00:00:00'),
        (626, '写作长度', 'ai_write_length', 0, '', '1', '2024-07-07 15:18:41', '1', '2024-07-07 15:18:41', false, '1970-01-01 00:00:00'),
        (627, '写作格式', 'ai_write_format', 0, '', '1', '2024-07-07 15:14:34', '1', '2024-07-07 15:14:34', false, '1970-01-01 00:00:00'),
        (628, 'AI 写作类型', 'ai_write_type', 0, '', '1', '2024-07-10 21:25:29', '1', '2024-07-10 21:25:29', false, '1970-01-01 00:00:00'),
        (629, 'BPM 流程模型类型', 'bpm_model_type', 0, '', '', '2025-01-08 17:07:13', '', '2025-01-08 17:07:13', false, null),
        (1836684901626863617, 'OA配置字段', 'OA_CONFIG_DICT', 0, '调用OA相关参数字典配置', '1', '2024-09-19 16:32:59', '1', '2024-09-19 16:32:59', false, '1970-01-01 00:00:00'),
        (1836684901626863618, '系统类型', 'system_biz_type', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2022-05-16 20:29:32', false, null),
        (1836684901626863619, '租户套餐包关系状态', 'tenant_package_relation_status', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2024-09-29 10:12:39', false, null),
        (1836684901626863620, '期限时间类型', 'tenant_date_term_type', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2024-09-24 09:42:19', false, null),
        (1836684901626863621, '员工状态', 'system_user_status', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2022-05-16 20:29:32', false, null),
        (1836684901626863622, '问诊形式', 'inquiry_way_type', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2022-05-16 20:29:32', false, null),
        (1836684901626863623, '套餐包收款方式', 'tenant_package_relation_payment_type', 0, null, 'admin', '2021-01-05 17:03:48', '1', '2024-09-29 14:37:39', false, null),
        (1838836937367760897, '互联网医院启用状态', 'hospital_disable_status', 0, '', '1', '2024-09-25 15:04:24', '1', '2024-09-25 15:04:24', false, '1970-01-01 00:00:00'),
        (1838908497889959938, '互联网医院等级', 'hospital_level', 0, '', '1', '2024-09-25 19:48:46', '1', '2024-09-25 19:48:46', false, '1970-01-01 00:00:00'),
        (1839136385266294786, '租户套餐签约渠道', 'tenant_package_relation_sign_channel', 0, '', '1', '2024-09-26 10:54:18', '1', '2024-09-29 14:44:12', false, '1970-01-01 00:00:00'),
        (1839137246008778753, '租户套餐收款账户', 'tenant_package_relation_collect_account', 0, '', '1', '2024-09-26 10:57:44', '1', '2024-09-29 14:44:13', false, '1970-01-01 00:00:00'),
        (1839234389304471554, '互联网医院是否有医保资质', 'hospital_has_medicare', 0, '', '1', '2024-09-26 17:23:44', '1', '2024-09-26 17:23:44', false, '1970-01-01 00:00:00'),
        (1839283543535841281, '科室等级', 'hospital_depart_level', 0, '', '1', '2024-09-26 20:39:04', '1', '2024-09-26 20:39:04', false, '1970-01-01 00:00:00'),
        (1839288707000889345, '医院科室状态', 'hospital_depart_status', 0, '', '1', '2024-09-26 20:59:35', '1', '2024-09-26 20:59:35', false, '1970-01-01 00:00:00'),
        (1839547641571786754, '套餐包性质', 'tenant_package_relation_nature', 0, '购买或赠送等', '1', '2024-09-27 14:08:30', '1', '2024-09-29 14:44:13', false, '1970-01-01 00:00:00'),
        (1840216225566371842, '租户套餐包关系退款类型', 'tenant_package_relation_refund_type', 0, '', '1', '2024-09-29 10:25:12', '1', '2024-09-29 10:25:12', false, '1970-01-01 00:00:00'),
        (1840258454369415170, '医生审核状态', 'doctor_audit_status', 0, '', '1', '2024-09-29 13:13:01', '1', '2024-09-29 13:13:01', false, '1970-01-01 00:00:00'),
        (1840258832406228994, '医生合作状态', 'doctor_cooperation_status', 0, '', '1', '2024-09-29 13:14:31', '1', '2024-09-29 13:14:31', false, '1970-01-01 00:00:00'),
        (1840259457444716546, '医生工作类型', 'doctor_job_type', 0, '', '1', '2024-09-29 13:17:00', '1', '2024-09-29 13:17:00', false, '1970-01-01 00:00:00'),
        (1840259924373639170, '医生出诊状态', 'doctor_online_status', 0, '', '1', '2024-09-29 13:18:51', '1', '2024-11-01 15:11:02', false, '1970-01-01 00:00:00'),
        (1840360601332457473, '专业职称', 'doctor_title', 0, '医生专业职称', '1', '2024-09-29 19:58:54', '1', '2025-01-22 16:31:13', false, '1970-01-01 00:00:00'),
        (1840371959746744322, '医生渠道', 'doctor_canal', 0, '', '1', '2024-09-29 20:44:02', '1', '2024-09-29 20:44:02', false, '1970-01-01 00:00:00'),
        (1840579752937168898, '民族', 'nation', 0, '', '1', '2024-09-30 10:29:44', '1', '2024-09-30 10:29:44', false, '1970-01-01 00:00:00'),
        (1840601120982474754, '学历', 'formal_level', 0, '', '1', '2024-09-30 11:54:39', '1', '2024-09-30 11:54:39', false, '1970-01-01 00:00:00'),
        (1843934685997895681, '系统菜单类型', 'system_menu_type', 0, '', '1', '2024-10-09 16:41:02', '1', '2024-10-09 16:41:02', false, '1970-01-01 00:00:00'),
        (1844293231790743553, '医生开方类型', 'doctor_inquiry_type', 0, '', '1', '2024-10-10 16:25:46', '1', '2024-10-10 16:25:46', false, '1970-01-01 00:00:00'),
        (1844612759940804609, '医生审核结果', 'doctor_audit_result', 0, '', '1', '2024-10-11 13:35:28', '1', '2024-10-11 13:35:28', false, '1970-01-01 00:00:00'),
        (1844944807851249665, '药师类型', 'pharmacist_type', 0, '', '1', '2024-10-12 11:34:54', '1', '2024-10-12 11:34:54', false, '1970-01-01 00:00:00'),
        (1844944996550762497, '药师资格', 'pharmacist_qualification', 0, '', '1', '2024-10-12 11:35:39', '1', '2024-10-12 11:35:39', false, '1970-01-01 00:00:00'),
        (1844945685553274881, '处方手绘签名', 'inquiry_drawn_sign', 0, '', '1', '2024-10-12 11:38:24', '1', '2024-10-12 11:39:30', false, '1970-01-01 00:00:00'),
        (1845034909218689025, '问诊业务类型', 'inquiry_biz_type', 0, '', '1', '2024-10-12 17:32:56', '1', '2024-10-12 17:32:56', false, '1970-01-01 00:00:00'),
        (1847161449379237889, '问诊状态', 'inquiry_status', 0, '', '1', '2024-10-18 14:23:03', '1', '2024-10-18 14:23:03', false, '1970-01-01 00:00:00'),
        (1847162368116363266, '问诊渠道', 'biz_channel_type', 0, '', '1', '2024-10-18 14:26:42', '1', '2024-10-18 14:26:42', false, '1970-01-01 00:00:00'),
        (1847207738741600257, '诊断类型', 'diagnosis_type', 0, '', '1', '2024-10-18 17:26:59', '1', '2024-10-18 17:26:59', false, '1970-01-01 00:00:00'),
        (1847208187444047874, '诊断性别限制', 'diagnosis_sex_limit', 0, '', '1', '2024-10-18 17:28:46', '1', '2024-10-18 17:28:46', false, '1970-01-01 00:00:00'),
        (1848323892168089601, '处方状态', 'prescription_status', 0, '', '1', '2024-10-21 19:22:11', '1', '2024-10-21 19:22:11', false, '1970-01-01 00:00:00'),
        (1848325053264683010, '处方用药类型', 'prescription_medicine_type', 0, '', '1', '2024-10-21 19:26:48', '1', '2024-10-21 19:26:48', false, '1970-01-01 00:00:00'),
        (1848616916085366786, '问诊客户端渠道类型', 'client_channel_type', 0, '', '1', '2024-10-22 14:46:33', '1', '2024-10-22 14:46:33', false, '1970-01-01 00:00:00'),
        (1848620424763269121, '是否复诊', 'further_inquiry', 0, '', '1', '2024-10-22 15:00:30', '1', '2024-10-22 15:00:30', false, '1970-01-01 00:00:00'),
        (1848621695297642497, '肝肾功能取值', 'liver_kidney_value', 0, '', '1', '2024-10-22 15:05:33', '1', '2024-10-22 15:05:33', false, '1970-01-01 00:00:00'),
        (1848622318948704257, '妊娠哺乳期取值', 'gestation_lactation_value', 0, '', '1', '2024-10-22 15:08:01', '1', '2024-10-22 15:08:01', false, '1970-01-01 00:00:00'),
        (1848643151515582465, '问诊客户端系统类型', 'client_os_type', 0, '', '1', '2024-10-22 16:30:48', '1', '2024-10-22 16:30:48', false, '1970-01-01 00:00:00'),
        (1849068986378686465, '处方日期类型', 'prescription_date_type', 0, '', '1', '2024-10-23 20:42:55', '1', '2024-10-23 20:42:55', false, '1970-01-01 00:00:00'),
        (1849738092354449409, '门店处方审方类型', 'prescription_drugstore_audit_type', 0, '', '1', '2024-10-25 17:01:42', '1', '2024-10-25 17:01:42', false, '1970-01-01 00:00:00'),
        (1850726577232105473, '处方笺模板类型', 'prescription_template_type', 0, '', '1', '2024-10-28 10:29:36', '1', '2024-10-28 10:29:36', false, '1970-01-01 00:00:00'),
        (1850823117804113922, '处方打印状态', 'prescription_print_status', 0, '', '1', '2024-10-28 16:53:13', '1', '2024-10-28 16:53:13', false, '1970-01-01 00:00:00'),
        (1850824955538411521, '医生客户端类型', 'doctor_channel_type', 0, '', '1', '2024-10-28 17:00:31', '1', '2024-10-28 17:00:31', false, '1970-01-01 00:00:00'),
        (1852263834026049538, '是否', 'yn_status', 0, '', '1', '2024-11-01 16:18:06', '1', '2024-11-01 16:18:06', false, '1970-01-01 00:00:00'),
        (1852265883090354178, '服务生效状态', 'tenant_package_effective_status', 0, '', '1', '2024-11-01 16:26:15', '1', '2024-11-01 16:31:36', false, '1970-01-01 00:00:00'),
        (1858355185475084289, '签章平台', 'inquiry_signature_platform', 0, '', '1', '2024-11-18 11:42:57', '1', '2024-11-18 11:42:57', false, '1970-01-01 00:00:00'),
        (1858417525458112513, '法大大用户证件类型', 'inquiry_signature_user_ident_type', 0, '', '1', '2024-11-18 15:50:40', '1', '2024-12-24 17:55:32', false, '1970-01-01 00:00:00'),
        (1858417848935419905, '法大大用户认证状态', 'inquiry_signature_user_auth_status', 0, '', '1', '2024-11-18 15:51:58', '1', '2024-12-24 17:55:32', false, '1970-01-01 00:00:00'),
        (1861283394495684610, '药品包装单位', 'drug_package_unit', 0, '', '1', '2024-11-26 13:38:37', '1', '2024-11-26 13:39:54', false, '1970-01-01 00:00:00'),
        (1861283590323544065, '给药单位', 'drug_dose_unit', 0, '药品剂量单位', '1', '2024-11-26 13:39:24', '1', '2025-01-22 16:29:43', false, '1970-01-01 00:00:00'),
        (1861284229258649602, '给药途径', 'drug_directions', 0, '药品使用方法', '1', '2024-11-26 13:41:56', '1', '2025-01-22 16:27:37', false, '1970-01-01 00:00:00'),
        (1861284373005836289, '给药频次', 'drug_use_frequency', 0, '药品使用频次', '1', '2024-11-26 13:42:30', '1', '2025-01-22 16:28:09', false, '1970-01-01 00:00:00'),
        (1871154460219117570, '中药使用方法', 'tcm_drug_directions', 0, '中药使用方法', '1', '2024-12-23 19:22:42', '1', '2024-12-23 19:24:00', false, '1970-01-01 00:00:00'),
        (1871154675110088706, '中药加工方式', 'tcm_processing_method', 0, '中药加工方式', '1', '2024-12-23 19:23:34', '1', '2024-12-23 19:23:34', false, '1970-01-01 00:00:00'),
        (1871376473076502529, '医生无需开方原因', 'doctor_reject_reason', 0, '医生无需开方原因', '1', '2024-12-24 10:04:54', '1', '2024-12-24 10:05:29', false, '1970-01-01 00:00:00'),
        (1871376556341825537, '药师审核驳回原因', 'pharmacist_reject_reason', 0, '药师审核驳回原因', '1', '2024-12-24 10:05:14', '1', '2024-12-24 10:05:14', false, '1970-01-01 00:00:00'),
        (1871462147993604097, '系统环境标识', 'system_env_tag', 0, '', '1', '2024-12-24 15:45:21', '1', '2024-12-24 15:45:21', false, '1970-01-01 00:00:00'),
        (1871475821722439682, '签章实名认证状态', 'inquiry_signature_certify_status', 0, '', '1', '2024-12-24 16:39:41', '1', '2024-12-24 16:39:41', false, '1970-01-01 00:00:00'),
        (1871494262193885185, '签章合同类型', 'inquiry_signature_contract_type', 0, '', '1', '2024-12-24 17:52:57', '1', '2024-12-24 17:52:57', false, '1970-01-01 00:00:00'),
        (1878991410833588226, '处方类型', 'prescription_type', 0, '', '1', '2025-01-14 10:23:57', '1', '2025-01-14 10:23:57', false, '1970-01-01 00:00:00'),
        (1881269022385713154, '商品-单位', 'product_unit', 0, '', '1', '2025-01-20 17:14:22', '1', '2025-01-20 17:14:22', false, '1970-01-01 00:00:00'),
        (1881269263855988738, '商品-剂型', 'product_dosage_form', 0, '', '1', '2025-01-20 17:15:19', '1', '2025-01-20 17:15:19', false, '1970-01-01 00:00:00'),
        (1881269263855988739, '商品-所属范围', 'product_business_scope', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988740, '商品-商品分类', 'product_product_category', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988741, '商品-自定义分类', 'product_custom_category', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988742, '商品-功能分类', 'product_function_category', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988743, '商品-处方分类', 'product_pres_category', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988744, '商品-储存条件', 'product_storage_way', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881269263855988745, '商品-用药提醒', 'product_medication_reminder', 0, null, '', '2025-01-20 17:21:16', '', '2025-01-20 17:21:16', false, null),
        (1881575475105443841, 'app版本更新类型', 'app_version_upgrade_type', 0, '', '1', '2025-01-21 13:32:06', '1', '2025-01-21 13:32:06', false, '1970-01-01 00:00:00'),
        (1881575963821551618, 'app版本更新范围', 'app_version_upgrade_scope', 0, '', '1', '2025-01-21 13:34:02', '1', '2025-01-21 13:34:09', false, '1970-01-01 00:00:00'),
        (1881584774544310274, '商品-税率', 'product_tax_rate', 0, '', '1', '2025-01-21 14:09:03', '1', '2025-01-21 14:09:03', false, '1970-01-01 00:00:00'),
        (1881679395075997698, '医生工作台状态', 'doctor_operat_floor_status', 0, '', '1', '2025-01-21 20:25:02', '1', '2025-01-21 20:25:02', false, '1970-01-01 00:00:00'),
        (1881995334033276929, '三方字典项', 'transmission_organ_dict', 0, '', '1', '2025-01-22 17:20:28', '1', '2025-01-23 11:11:59', false, '1970-01-01 00:00:00'),
        (1882270272283701250, '中医治法', 'tcm_treatment_methods', 0, '', '1', '2025-01-23 11:32:58', '1', '2025-01-23 11:32:58', false, '1970-01-01 00:00:00'),
        (1882270302545604609, '中医证候', 'tcm_syndrome', 0, '', '1', '2025-01-23 11:33:06', '1', '2025-01-23 11:33:06', false, '1970-01-01 00:00:00'),
        (1882657984378396674, 'app_version_disable', 'app版本启禁用状态', 0, '', '1', '2025-01-24 13:13:36', '1', '2025-01-24 13:14:43', true, '2025-01-24 13:14:45'),
        (1882658416886636546, 'app版本启禁用状态', 'app_version_disable_status', 0, '', '1', '2025-01-24 13:15:19', '1', '2025-01-24 13:15:19', false, '1970-01-01 00:00:00'),
        (1882663310142038017, 'app已发布的应用市场', 'app_store_released_channel', 0, '', '1', '2025-01-24 13:34:46', '1', '2025-01-24 13:34:46', false, '1970-01-01 00:00:00'),
        (1887395762793512961, '商品-首营审批状态', 'product_first_approval_status', 0, '', '1', '2025-02-06 14:59:51', '1', '2025-02-06 15:00:05', false, '1970-01-01 00:00:00'),
        (1887396734563418113, '商品-资质类型', 'product_qualification_type', 0, '', '1', '2025-02-06 15:03:42', '1', '2025-02-06 15:03:42', false, '1970-01-01 00:00:00'),
        (1887396798358781954, '商品-流转同步类型', 'product_transfer_sync_type', 0, '', '1', '2025-02-06 15:03:58', '1', '2025-02-06 15:03:58', false, '1970-01-01 00:00:00'),
        (1887396859742420994, '商品-流转同步状态', 'product_transfer_sync_status', 0, '', '1', '2025-02-06 15:04:12', '1', '2025-02-06 15:04:12', false, '1970-01-01 00:00:00'),
        (1887396914503254017, '商品-质量变更类型', 'product_quality_change_type', 0, '', '1', '2025-02-06 15:04:25', '1', '2025-02-06 15:04:25', false, '1970-01-01 00:00:00'),
        (1887396971113775105, '商品-审批流业务类型', 'product_bpm_business_type', 0, '', '1', '2025-02-06 15:04:39', '1', '2025-02-06 15:04:39', false, '1970-01-01 00:00:00'),
        (1887397105797070850, '商品-审批流状态', 'product_bpm_approval_status', 0, '', '1', '2025-02-06 15:05:11', '1', '2025-02-06 15:05:11', false, '1970-01-01 00:00:00'),
        (1897272280747261953, '商品-状态', 'product_status', 0, '', '1', '2025-03-05 21:05:36', '1', '2025-03-05 21:05:36', false, '1970-01-01 00:00:00'),
        (1897469121470869505, 'CA应用机构', 'inquiry_signature_platform_ca_app', 0, '', '1', '2025-03-06 10:07:46', '1', '2025-03-14 11:35:54', false, '1970-01-01 00:00:00'),
        (1897560430201499650, '三方字典匹配状态', 'transmission_dict_match_status', 0, '', '1', '2025-03-06 16:10:36', '1', '2025-03-06 16:10:36', false, '1970-01-01 00:00:00'),
        (1897631271204044802, '科室字典', 'dept_dict', 0, '', '1', '2025-03-06 20:52:06', '1', '2025-03-06 20:52:06', false, '1970-01-01 00:00:00'),
        (1898991704734908417, '三方服务商机构类型', 'transmission_organ_type', 0, '', '1', '2025-03-10 14:57:59', '1', '2025-03-10 14:57:59', false, '1970-01-01 00:00:00'),
        (1899788118819581953, '转诊标识', 'inquiry_referral', 0, '', '1', '2025-03-12 19:42:39', '1', '2025-03-12 19:42:39', false, '1970-01-01 00:00:00'),
        (1900383777411006465, '荷叶门店类型', 'inquiry_tenant_type', 0, '', '1894617450666696706', '2025-03-14 11:09:35', '1894617450666696706', '2025-03-14 11:09:35', false, '1970-01-01 00:00:00'),
        (1904409511679733762, '门店服务包开通状态', 'tenant_service_pack_relation_status', 0, '', '1', '2025-03-25 13:46:24', '1', '2025-03-25 13:46:24', false, '1970-01-01 00:00:00'),
        (1904416600845443074, '目录环境', 'catalog_env', 0, '', '1', '2025-03-25 14:14:35', '1', '2025-03-25 14:14:35', false, '1970-01-01 00:00:00'),
        (1904444634764656641, '传输服务请求三方状态', 'transmission_record_request_status', 0, '', '1', '2025-03-25 16:05:58', '1', '2025-03-25 16:05:58', false, '1970-01-01 00:00:00'),
        (1905533787167776770, '商品-大类', 'product_spu_category', 0, '', '1', '2025-03-28 16:13:53', '1', '2025-03-28 16:13:53', false, '1970-01-01 00:00:00'),
        (1906676614108794881, '配置包配置类型', 'transmission_dsl_type', 0, '', '1', '2025-03-31 19:55:04', '1', '2025-03-31 19:55:04', false, '1970-01-01 00:00:00'),
        (1907610144636612610, '服务环境类型', 'transmission_service_env', 0, '', '1', '2025-04-03 09:44:35', '1', '2025-04-03 14:55:44', false, '1970-01-01 00:00:00'),
        (1907639479825281025, '传输服务节点类型', 'transmission_node_type', 0, '', '1', '2025-04-03 11:41:09', '1', '2025-04-03 11:41:09', false, '1970-01-01 00:00:00'),
        (1909872258734551042, '新品提报审核类型', 'system_audit_type', 0, '系统管理下-新品提报列表', '1', '2025-04-09 15:33:25', '1', '2025-04-09 19:04:08', true, '2025-04-09 19:04:09'),
        (1909897933591867393, '用户账号状态', 'system_user_account_status', 0, '', '1', '2025-04-09 17:15:26', '1', '2025-04-09 17:15:26', false, '1970-01-01 00:00:00'),
        (1909924072045637633, '新品提报审核状态', 'product_present_approve_status', 0, '', '1', '2025-04-09 18:59:18', '1', '2025-04-09 18:59:18', false, '1970-01-01 00:00:00'),
        (1912799017647824897, '患者小程序问诊是否需审核', 'consulation_review_required', 0, '', '1', '2025-04-17 17:23:19', '1', '2025-04-17 17:23:19', false, '1970-01-01 00:00:00'),
        (1914505427138199554, '医院药师取值', 'signature_seal_value_type', 0, '', '1', '2025-04-22 10:23:58', '1', '2025-04-22 10:23:58', false, '1970-01-01 00:00:00'),
        (1914935761376272386, '药师性质', 'pharmacist_nature_type', 0, '', '1', '2025-04-23 14:53:58', '1', '2025-04-23 14:53:58', false, '1970-01-01 00:00:00'),
        (1914954149688115201, '问诊审方类型', 'inquiry_audit_type', 0, '', '1', '2025-04-23 16:07:02', '1', '2025-04-23 16:07:02', false, '1970-01-01 00:00:00'),
        (1916360574352842753, '慢病病情需要', 'CHRONIC_DISEASE_CONDITION', 0, '', '1', '2025-04-27 13:15:40', '1', '2025-04-27 13:19:51', true, '2025-04-27 13:19:51'),
        (1916361685218451458, '慢病病需要', 'chronic_disease_condition', 0, '', '1', '2025-04-27 13:20:05', '1', '2025-04-27 13:24:06', true, '2025-04-27 13:24:06'),
        (1916362915739164673, '慢病病情需要', 'chronic_disease_condition_requires', 0, '', '1', '2025-04-27 13:24:58', '1', '2025-04-27 13:24:58', false, '1970-01-01 00:00:00'),
        (1930921830059642882, '迁移方式', 'migration_action', 0, '', '1', '2025-06-06 17:36:54', '1', '2025-06-06 17:36:54', false, '1970-01-01 00:00:00'),
        (1930922085887021058, '迁移状态', 'migration_status', 0, '', '1', '2025-06-06 17:37:55', '1', '2025-06-06 17:37:55', false, '1970-01-01 00:00:00'),
        (1931906497302794242, '含特殊药品复方制剂', 'product_special_medicine_preparation', 0, '', '1', '2025-06-09 10:49:37', '1', '2025-06-09 10:49:37', false, '1970-01-01 00:00:00'),
        (1931908683155935233, '商品标签', 'product_product_lable', 0, '', '1', '2025-06-09 10:58:18', '1', '2025-06-09 10:58:18', false, '1970-01-01 00:00:00'),
        (1931910314547253249, '医保匹配状态', 'product_medical_insurance_state', 0, '', '1', '2025-06-09 11:04:47', '1', '2025-06-09 11:04:47', false, '1970-01-01 00:00:00'),
        (1931912944602353666, '是否有标识码', 'product_identification_code', 0, '', '1', '2025-06-09 11:15:14', '1', '2025-06-09 11:15:14', false, '1970-01-01 00:00:00'),
        (1931914598684856322, '资质是否过期', 'product_qualifications_expire', 0, '', '1', '2025-06-09 11:21:48', '1', '2025-06-09 11:21:48', false, '1970-01-01 00:00:00'),
        (1931918897116647426, '停售状态', 'product_suspension_status', 0, '', '1', '2025-06-09 11:38:53', '1', '2025-06-09 11:38:53', false, '1970-01-01 00:00:00'),
        (1931919155859066881, '禁采状态', 'product_prohibited_mining_status', 0, '', '1', '2025-06-09 11:39:55', '1', '2025-06-09 11:39:55', false, '1970-01-01 00:00:00'),
        (1931919872564957186, '商品-审批状态', 'product_audit_status', 0, '', '1', '2025-06-09 11:42:46', '1', '2025-06-09 11:42:46', false, '1970-01-01 00:00:00'),
        (1931920370726637569, '商品-医保限制', 'product_medical_insurance_restrictions', 0, '', '1', '2025-06-09 11:44:44', '1', '2025-06-09 11:44:44', false, '1970-01-01 00:00:00'),
        (1931920613010608129, '商品-创建人', 'product_founder', 0, '', '1', '2025-06-09 11:45:42', '1', '2025-06-09 11:46:00', true, '2025-06-09 11:46:01'),
        (1932000027289956353, '迁移点状态', 'migration_point_status', 0, '', '1', '2025-06-09 17:01:16', '1', '2025-06-09 17:01:16', false, '1970-01-01 00:00:00'),
        (1933059167776235522, '问诊额度记录类型', 'package_cost_record_type', 0, '', '1', '2025-06-12 15:09:55', '1', '2025-06-12 15:09:55', false, '1970-01-01 00:00:00'),
        (1934519305581494274, '商品删除类型', 'product_delete_type', 0, '', '1', '2025-06-16 15:51:59', '1', '2025-06-16 15:51:59', false, '1970-01-01 00:00:00'),
        (1934911795085844482, 'system_business_line', '系统业务线', 0, '', '1', '2025-06-17 17:51:36', '1', '2025-06-17 17:54:00', true, '2025-06-17 17:54:00'),
        (1935625136023597058, '链路追踪节点', 'inquiry_trace_node', 0, '', '1', '2025-06-19 17:06:09', '1', '2025-06-19 17:06:09', false, '1970-01-01 00:00:00'),
        (1938124617742790659, '门店类型', 'tenant_type', 0, '', '', '2025-06-30 11:27:57', '', '2025-06-30 11:27:57', false, null),
        (1938124617742790660, '套餐类型', 'tenant_package_type', 0, '', '', '2025-06-30 11:34:57', '', '2025-06-30 11:34:57', false, null),
        (1939602952154210306, '迁移类型', 'migration_type', 0, '', '1', '2025-06-30 16:32:35', '1', '2025-06-30 16:32:35', false, '1970-01-01 00:00:00');