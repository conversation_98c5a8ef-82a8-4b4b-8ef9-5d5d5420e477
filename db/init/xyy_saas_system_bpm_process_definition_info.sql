insert into xyy_saas_system.bpm_process_definition_info (id, process_definition_id, model_id, model_type, icon, description, form_type, form_id, form_conf, form_fields, form_custom_create_path, form_custom_view_path, simple_model, sort, visible, start_user_ids, manager_user_ids, creator, create_time, updater, update_time, deleted, tenant_id)
values  (141, 'test_return:1:dd6b6e1d-5ac0-11ee-be77-fe3a43e3aa2f', '75452338-5ac0-11ee-be77-fe3a43e3aa2f', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2023-09-24 17:58:09', '1', '2023-09-24 17:58:09', false, 1),
        (142, 'test_return2:1:e2de03c7-5ac2-11ee-9bee-fe3a43e3aa2f', 'ad8d0722-5ac2-11ee-9bee-fe3a43e3aa2f', 10, null, 'biubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2023-09-24 18:12:37', '1', '2023-09-24 18:12:37', false, 1),
        (143, 'test_return2:2:320f7403-5ac3-11ee-9bee-fe3a43e3aa2f', 'ad8d0722-5ac2-11ee-9bee-fe3a43e3aa2f', 10, null, 'biubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2023-09-24 18:14:50', '1', '2023-09-24 18:14:50', false, 1),
        (144, 'contract-approve:1:057a9d3b-d1ed-11ee-a436-8a9af48de719', '499c96e6-d1ec-11ee-a436-8a9af48de719', 10, null, null, 20, null, null, null, '1', '/crm/contract/detail', '1', 0, true, null, null, '1', '2024-02-23 09:44:02', '1', '2024-02-23 09:44:02', false, 1),
        (145, 'contract-approve:2:dd644004-d208-11ee-ba3b-5e0431cb568f', '499c96e6-d1ec-11ee-a436-8a9af48de719', 10, null, null, 20, null, null, null, '/crm/contract/detail2', '/crm/contract/detail2', '1', 0, true, null, null, '1', '2024-02-23 13:03:21', '1', '2024-02-23 13:03:21', false, 1),
        (146, 'contract-approve:3:87c93a24-d212-11ee-ba3b-5e0431cb568f', '499c96e6-d1ec-11ee-a436-8a9af48de719', 10, null, null, 20, null, null, null, '/crm/contract/detail/index', '/crm/contract/detail/index', '1', 0, true, null, null, '1', '2024-02-23 14:12:32', '1', '2024-02-23 14:12:32', false, 1),
        (147, 'crm-contract-audit:1:a5ec9240-d2a7-11ee-a703-92ff8e2e5a3e', '5b40f87b-d2a7-11ee-a703-92ff8e2e5a3e', 10, null, null, 20, null, null, null, '/crm/business/detail/index', '/crm/business/detail/index', '1', 0, true, null, null, '1', '2024-02-24 07:59:58', '1', '2024-02-24 07:59:58', false, 1),
        (148, 'crm-contract-audit:2:288f69f6-d2a8-11ee-b20a-8adfac90dc14', '5b40f87b-d2a7-11ee-a703-92ff8e2e5a3e', 10, null, null, 20, null, null, null, '/crm/contract/detail/index', '/crm/contract/detail/index', '1', 0, true, null, null, '1', '2024-02-24 08:03:37', '1', '2024-02-24 08:03:37', false, 1),
        (149, 'crm-receivable-audit:1:fa8272f3-d3d2-11ee-aa2f-26aa5e0b65cc', '9bc054ce-d3d2-11ee-aa2f-26aa5e0b65cc', 10, null, null, 20, null, null, null, '/crm/receivable/detail/index', '/crm/receivable/detail/index', '1', 0, true, null, null, '1', '2024-02-25 19:42:39', '1', '2024-02-25 19:42:39', false, 1),
        (150, 'xx:1:4d87fd62-e074-11ee-9b46-0e22e0b61031', '9f50553c-a6b4-11ee-904d-2a0706c917c0', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-12 21:27:42', '1', '2024-03-12 21:27:42', false, 1),
        (151, 'xx:2:eacda6dc-e077-11ee-be9b-f21fd1da61a2', '9f50553c-a6b4-11ee-904d-2a0706c917c0', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-12 21:53:34', '1', '2024-03-12 21:53:34', false, 1),
        (152, 'new-1:1:73f06beb-e13b-11ee-a38f-06b2c2ddc7b1', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-13 21:13:16', '1', '2024-03-13 21:13:16', false, 1),
        (153, 'new-1:2:7f2c569a-e141-11ee-9d86-06b2c2ddc7b1', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-13 21:56:32', '1', '2024-03-13 21:56:32', false, 1),
        (154, 'new-1:3:86ec704e-e141-11ee-9d86-06b2c2ddc7b1', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-13 21:56:45', '1', '2024-03-13 21:56:45', false, 1),
        (155, 'new-1:4:6a258922-e1bc-11ee-9330-feeee51d4267', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 12:36:25', '1', '2024-03-14 12:36:25', false, 1),
        (156, 'new-1:5:a48c8e16-e1bc-11ee-9330-feeee51d4267', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 12:38:03', '1', '2024-03-14 12:38:03', false, 1),
        (157, 'new-1:6:ab7ad8da-e1bc-11ee-9330-feeee51d4267', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 12:38:14', '1', '2024-03-14 12:38:14', false, 1),
        (158, 'new-1:7:d7595072-e1bc-11ee-9b9f-feeee51d4267', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 12:39:28', '1', '2024-03-14 12:39:28', false, 1),
        (159, 'new-1:8:75b9842a-e205-11ee-aac1-66c23f24ce2b', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 21:19:17', '1', '2024-03-14 21:19:17', false, 1),
        (160, 'new-1:9:9dcaa07e-e205-11ee-aac1-66c23f24ce2b', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 21:20:25', '1', '2024-03-14 21:20:25', false, 1),
        (161, 'new-1:10:9f5a6842-e205-11ee-aac1-66c23f24ce2b', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 21:20:27', '1', '2024-03-14 21:20:27', false, 1),
        (162, 'new-1:11:f2499f4b-e20c-11ee-beae-46aa22562164', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-14 22:12:53', '1', '2024-03-14 22:12:53', false, 1),
        (163, 'new-2:1:7fc84238-e2c1-11ee-ad8d-9216688a4b8f', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-15 19:45:20', '1', '2024-03-15 19:45:20', false, 1),
        (164, 'new-2:2:18c21844-e2c2-11ee-ad8d-9216688a4b8f', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-15 19:49:36', '1', '2024-03-15 19:49:36', false, 1),
        (165, 'new-2:3:2a67c3b8-e2c2-11ee-ad8d-9216688a4b8f', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-15 19:50:06', '1', '2024-03-15 19:50:06', false, 1),
        (166, 'new-2:4:4bdfb93e-e2c2-11ee-ad8d-9216688a4b8f', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-15 19:51:02', '1', '2024-03-15 19:51:02', false, 1),
        (167, 'new-2:5:aca8925e-e2e9-11ee-83a1-0e2aa205f435', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 00:32:55', '1', '2024-03-16 00:32:55', false, 1),
        (168, 'new-2:6:634784f8-e2ea-11ee-b183-0e2aa205f435', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 00:38:01', '1', '2024-03-16 00:38:01', false, 1),
        (169, 'new-2:7:8167c4f2-e2ea-11ee-b183-0e2aa205f435', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, null, '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 00:38:52', '1', '2024-03-16 00:38:52', false, 1),
        (170, 'test:1:9c4cac8d-e360-11ee-9498-6aca1e3be435', '7077f888-e360-11ee-9498-6aca1e3be435', 10, null, 'biubiubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 14:44:17', '1', '2024-03-16 14:44:17', false, 1),
        (171, 'test:2:99e30b8e-e370-11ee-a1a2-0ebfd53b3c91', '7077f888-e360-11ee-9498-6aca1e3be435', 10, null, 'biubiubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 16:38:45', '1', '2024-03-16 16:38:45', false, 1),
        (172, 'test:3:9befa472-e370-11ee-a1a2-0ebfd53b3c91', '7077f888-e360-11ee-9498-6aca1e3be435', 10, null, 'biubiubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 16:38:49', '1', '2024-03-16 16:38:49', false, 1),
        (173, 'huoqian:1:e95f383c-e37e-11ee-a889-0ebfd53b3c91', 'd139e257-e37e-11ee-a889-0ebfd53b3c91', 10, null, '123321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 18:21:12', '1', '2024-03-16 18:21:12', false, 1),
        (174, 'bohui:1:5b1ab874-e384-11ee-9e7b-aab4713d012e', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-16 19:00:10', '1', '2024-03-16 19:00:10', false, 1),
        (175, 'bohui:2:929c1199-e5ca-11ee-9e7b-3e9a6d6b4cd2', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, '测试', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 16:27:50', '1', '2024-03-19 16:27:50', false, 1),
        (176, 'bohui:3:2e6a6c9d-e5e2-11ee-9e7b-3e9a6d6b4cd2', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, '测试', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:16:50', '1', '2024-03-19 19:16:50', false, 1),
        (177, 'huoqian:2:3081daf1-e5e2-11ee-9e7b-3e9a6d6b4cd2', 'd139e257-e37e-11ee-a889-0ebfd53b3c91', 10, null, '312321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:16:53', '1', '2024-03-19 19:16:53', false, 1),
        (178, 'test:4:31c77a55-e5e2-11ee-9e7b-3e9a6d6b4cd2', '7077f888-e360-11ee-9498-6aca1e3be435', 10, null, 'biubiubiu', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:16:56', '1', '2024-03-19 19:16:56', false, 1),
        (179, 'new-1:12:32ec9969-e5e2-11ee-9e7b-3e9a6d6b4cd2', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:16:57', '1', '2024-03-19 19:16:57', false, 1),
        (180, 'new-1:13:8612ea4d-e5e2-11ee-9e7b-3e9a6d6b4cd2', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:19:17', '1', '2024-03-19 19:19:17', false, 1),
        (181, 'crm-receivable-audit:2:87747621-e5e2-11ee-9e7b-3e9a6d6b4cd2', '9bc054ce-d3d2-11ee-aa2f-26aa5e0b65cc', 10, null, null, 20, null, null, null, '/crm/receivable/detail/index', '/crm/receivable/detail/index', '1', 0, true, null, null, '1', '2024-03-19 19:19:19', '1', '2024-03-19 19:19:19', false, 1),
        (182, 'huoqian:3:a0f2f365-e5e2-11ee-9e7b-3e9a6d6b4cd2', 'd139e257-e37e-11ee-a889-0ebfd53b3c91', 10, null, '312321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 19:20:02', '1', '2024-03-19 19:20:02', false, 1),
        (183, 'bohui:4:0ae36c02-e607-11ee-95fc-76ce04f8068a', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, '测试', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-19 23:40:42', '1', '2024-03-19 23:40:42', false, 1),
        (184, 'bohui:5:cc1ecb1f-e60c-11ee-9a09-76ce04f8068a', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, '测试', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 00:21:53', '1', '2024-03-20 00:21:53', false, 1),
        (185, 'bohui:6:cd95b3b3-e60c-11ee-9a09-76ce04f8068a', '35963a6f-e384-11ee-9e7b-aab4713d012e', 10, null, '测试', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 00:21:56', '1', '2024-03-20 00:21:56', false, 1),
        (186, 'huoqian:4:e4f8f217-e60c-11ee-9a09-76ce04f8068a', 'd139e257-e37e-11ee-a889-0ebfd53b3c91', 10, null, '312321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 00:22:35', '1', '2024-03-20 00:22:35', false, 1),
        (187, 'huoqian:5:e87101db-e60c-11ee-9a09-76ce04f8068a', 'd139e257-e37e-11ee-a889-0ebfd53b3c91', 10, null, '312321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 00:22:41', '1', '2024-03-20 00:22:41', false, 1),
        (188, 'new-test:1:c94e33b3-e60e-11ee-b246-76ce04f8068a', '89f1f488-e60e-11ee-9a09-76ce04f8068a', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 00:36:08', '1', '2024-03-20 00:36:08', false, 1),
        (189, 'new-test:2:09735826-e66a-11ee-9c22-4aafd11823fb', '89f1f488-e60e-11ee-9a09-76ce04f8068a', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 11:29:19', '1', '2024-03-20 11:29:19', false, 1),
        (190, 'new-test:3:0abe009a-e66a-11ee-9c22-4aafd11823fb', '89f1f488-e60e-11ee-9a09-76ce04f8068a', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 11:29:22', '1', '2024-03-20 11:29:22', false, 1),
        (191, 'hhhh:1:8b313806-e698-11ee-8b05-52c6ce924391', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, null, null, null, null, '1', 0, true, null, null, '1', '2024-03-20 17:02:14', '1', '2024-03-20 17:02:14', false, 1),
        (192, 'hhhh:2:b96ff12b-e699-11ee-a9b4-52c6ce924391', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-20 17:10:41', '1', '2024-03-20 17:10:41', false, 1),
        (193, 'oa_leave:1:7f7be21c-e6b8-11ee-9cdc-4a23a0fa7ecb', '5a910c07-e6b8-11ee-9cdc-4a23a0fa7ecb', 10, null, null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-20 20:50:58', '1', '2024-03-20 20:50:58', false, 1),
        (194, 'oa_leave:2:dc565ee3-e841-11ee-a5e7-525da23008fe', '5a910c07-e6b8-11ee-9cdc-4a23a0fa7ecb', 10, null, null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-22 19:46:46', '1', '2024-03-22 19:46:46', false, 1),
        (195, 'hhhh:3:c1e2dce5-e842-11ee-90ce-4a385f3b4f1a', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-22 19:53:11', '1', '2024-03-22 19:53:11', false, 1),
        (196, 'hhhh:4:d27088a9-e842-11ee-90ce-4a385f3b4f1a', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-22 19:53:39', '1', '2024-03-22 19:53:39', false, 1),
        (197, 'oa_leave:3:ec4b0352-e85f-11ee-a915-fef401dd8c67', '5a910c07-e6b8-11ee-9cdc-4a23a0fa7ecb', 10, null, null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-22 23:21:58', '1', '2024-03-22 23:21:58', false, 1),
        (198, 'hhhh:5:f8052e06-e85f-11ee-a915-fef401dd8c67', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-22 23:22:18', '1', '2024-03-22 23:22:18', false, 1),
        (199, 'hhhh:6:f9c52d8a-e85f-11ee-a915-fef401dd8c67', '155910bd-e698-11ee-aeba-aa5b9845b3dd', 10, null, '444', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-22 23:22:21', '1', '2024-03-22 23:22:21', false, 1),
        (200, 'oa_leave:4:405a9670-e86a-11ee-9962-52c5f2f50e3f', '5a910c07-e6b8-11ee-9cdc-4a23a0fa7ecb', 10, null, null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-23 00:35:54', '1', '2024-03-23 00:35:54', false, 1),
        (201, 'test-execute-listener:1:53f38c0e-e8c7-11ee-a81e-aa3623b10f85', '63ad6275-e8c6-11ee-a81e-aa3623b10f85', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 11:42:10', '1', '2024-03-23 11:42:10', false, 1),
        (202, 'test-execute-listener:2:a385e0a9-e8c7-11ee-aad0-aa3623b10f85', '63ad6275-e8c6-11ee-a81e-aa3623b10f85', 10, null, null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 11:44:23', '1', '2024-03-23 11:44:23', false, 1),
        (203, 'jiandan:1:9303634e-e8c8-11ee-afed-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 11:51:05', '1', '2024-03-23 11:51:05', false, 1),
        (204, 'jiandan:2:a188dba5-e8cb-11ee-90b0-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:12:58', '1', '2024-03-23 12:12:58', false, 1),
        (205, 'jiandan:3:a365b1f9-e8cb-11ee-90b0-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:13:01', '1', '2024-03-23 12:13:01', false, 1),
        (206, 'jiandan:4:cd840373-e8cb-11ee-90b0-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:14:12', '1', '2024-03-23 12:14:12', false, 1),
        (207, 'jiandan:5:eaa4117f-e8cb-11ee-90b0-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:15:01', '1', '2024-03-23 12:15:01', false, 1),
        (208, 'jiandan:6:86693c4b-e8cc-11ee-90b0-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:19:22', '1', '2024-03-23 12:19:22', false, 1),
        (209, 'jiandan:7:05a77968-e8cf-11ee-817e-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:37:15', '1', '2024-03-23 12:37:15', false, 1),
        (210, 'jiandan:8:40e137b4-e8cf-11ee-817e-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:38:54', '1', '2024-03-23 12:38:54', false, 1),
        (211, 'jiandan:9:5df007b3-e8cf-11ee-817e-aa3623b10f85', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 12:39:43', '1', '2024-03-23 12:39:43', false, 1),
        (212, 'jiandan:10:86a7834f-e904-11ee-868e-769cf7027e4a', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:00:14', '1', '2024-03-23 19:00:14', false, 1),
        (213, 'jiandan:11:ae58a559-e904-11ee-868e-769cf7027e4a', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:01:21', '1', '2024-03-23 19:01:21', false, 1),
        (214, 'jiandan:12:b021305d-e904-11ee-868e-769cf7027e4a', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:01:24', '1', '2024-03-23 19:01:24', false, 1),
        (215, 'jiandan:13:3d5f2d56-e905-11ee-9c2f-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:05:21', '1', '2024-03-23 19:05:21', false, 1),
        (216, 'jiandan:14:911254cd-e905-11ee-b8a7-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:07:41', '1', '2024-03-23 19:07:41', false, 1),
        (217, 'jiandan:15:d171abcd-e905-11ee-b8a7-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:09:29', '1', '2024-03-23 19:09:29', false, 1),
        (218, 'jiandan:16:12cd3d1d-e906-11ee-b8a7-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:11:19', '1', '2024-03-23 19:11:19', false, 1),
        (219, 'jiandan:17:14ffd491-e906-11ee-b8a7-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:11:23', '1', '2024-03-23 19:11:23', false, 1),
        (220, 'jiandan:18:a37d1331-e907-11ee-ad58-cacdb29edc0f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 19:22:31', '1', '2024-03-23 19:22:31', false, 1),
        (221, 'jiandan:19:c0a44484-e915-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:03:33', '1', '2024-03-23 21:03:33', false, 1),
        (222, 'jiandan:20:d94ad861-e915-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:04:14', '1', '2024-03-23 21:04:14', false, 1),
        (223, 'jiandan:21:ebf8cc15-e915-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:04:46', '1', '2024-03-23 21:04:46', false, 1),
        (224, 'jiandan:22:f303eda9-e915-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:04:58', '1', '2024-03-23 21:04:58', false, 1),
        (225, 'jiandan:23:1f51a246-e916-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:06:12', '1', '2024-03-23 21:06:12', false, 1),
        (226, 'jiandan:24:2143602a-e916-11ee-9b58-4ad6071fe8cb', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-23 21:06:15', '1', '2024-03-23 21:06:15', false, 1),
        (227, 'jiandan:25:fe687100-e97f-11ee-98a3-7248bcbce76f', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 09:44:03', '1', '2024-03-24 09:44:03', false, 1),
        (228, 'jiandan:26:e9f49fcc-e981-11ee-b9f2-daad0f82ed42', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, null, '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 09:57:48', '1', '2024-03-24 09:57:48', false, 1),
        (229, 'jiandan:27:1fdd973f-e982-11ee-85e4-0ae4354b692e', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 09:59:19', '1', '2024-03-24 09:59:19', false, 1),
        (230, 'jiandan:28:4eaa2e83-e982-11ee-85e4-0ae4354b692e', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 10:00:45', '1', '2024-03-24 10:00:45', false, 1),
        (231, 'crm-receivable-audit:3:a7ef6c37-e982-11ee-85e4-0ae4354b692e', '9bc054ce-d3d2-11ee-aa2f-26aa5e0b65cc', 10, 'http://test.yudao.iocoder.cn/4c38186a52a6796d998f4e826178e1c24bee0eb722f8751e9ffb4d8eaea9b5cf.png', null, 20, null, null, null, '/crm/receivable/detail/index', '/crm/receivable/detail/index', '1', 0, true, null, null, '1', '2024-03-24 10:03:07', '1', '2024-03-24 10:03:07', false, 1),
        (232, 'new-1:14:bddcd37b-e982-11ee-85e4-0ae4354b692e', '341a1b12-e13b-11ee-a38f-06b2c2ddc7b1', 10, null, '2222', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 10:03:44', '1', '2024-03-24 10:03:44', false, 1),
        (233, 'crm-receivable-audit:4:bf5cbcbf-e982-11ee-85e4-0ae4354b692e', '9bc054ce-d3d2-11ee-aa2f-26aa5e0b65cc', 10, 'http://test.yudao.iocoder.cn/4c38186a52a6796d998f4e826178e1c24bee0eb722f8751e9ffb4d8eaea9b5cf.png', null, 20, null, null, null, '/crm/receivable/detail/index', '/crm/receivable/detail/index', '1', 0, true, null, null, '1', '2024-03-24 10:03:46', '1', '2024-03-24 10:03:46', false, 1),
        (234, 'crm-contract-audit:3:c0998283-e982-11ee-85e4-0ae4354b692e', '5b40f87b-d2a7-11ee-a703-92ff8e2e5a3e', 10, 'http://test.yudao.iocoder.cn/10d745dfe02082351f86c28ed2f3a7e0da8bbf6858f1d4a02e409930b3670d63.png', null, 20, null, null, null, '/crm/contract/detail/index', '/crm/contract/detail/index', '1', 0, true, null, null, '1', '2024-03-24 10:03:48', '1', '2024-03-24 10:03:48', false, 1),
        (235, 'jiandan:29:04a89a9e-e983-11ee-8309-e2652d1d3e17', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 10:05:42', '1', '2024-03-24 10:05:42', false, 1),
        (236, 'test-execute-listener:3:05563fc2-e983-11ee-8309-e2652d1d3e17', '63ad6275-e8c6-11ee-a81e-aa3623b10f85', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 10:05:43', '1', '2024-03-24 10:05:43', false, 1),
        (237, 'oa_leave:5:06357c36-e983-11ee-8309-e2652d1d3e17', '5a910c07-e6b8-11ee-9cdc-4a23a0fa7ecb', 10, 'http://test.yudao.iocoder.cn/628560c644de0ae9120114509227ffb90a32e23dfc9afc2bd63763cd781ece8f.png', null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-24 10:05:45', '1', '2024-03-24 10:05:45', false, 1),
        (238, 'new-2:8:072f458a-e983-11ee-8309-e2652d1d3e17', '096c61cd-e2c0-11ee-ad8d-9216688a4b8f', 10, 'http://test.yudao.iocoder.cn/6857d7b2e57f514629c0f1aa937c5fc6c551460851975bc3ea15b5a56ce4bdea.png', '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 10:05:47', '1', '2024-03-24 10:05:47', false, 1),
        (239, 'crm-receivable-audit:5:0828729e-e983-11ee-8309-e2652d1d3e17', '9bc054ce-d3d2-11ee-aa2f-26aa5e0b65cc', 10, 'http://test.yudao.iocoder.cn/4c38186a52a6796d998f4e826178e1c24bee0eb722f8751e9ffb4d8eaea9b5cf.png', null, 20, null, null, null, '/crm/receivable/detail/index', '/crm/receivable/detail/index', '1', 0, true, null, null, '1', '2024-03-24 10:05:48', '1', '2024-03-24 10:05:48', false, 1),
        (240, 'crm-contract-audit:4:09a0e1d2-e983-11ee-8309-e2652d1d3e17', '5b40f87b-d2a7-11ee-a703-92ff8e2e5a3e', 10, 'http://test.yudao.iocoder.cn/10d745dfe02082351f86c28ed2f3a7e0da8bbf6858f1d4a02e409930b3670d63.png', null, 20, null, null, null, '/crm/contract/detail/index', '/crm/contract/detail/index', '1', 0, true, null, null, '1', '2024-03-24 10:05:51', '1', '2024-03-24 10:05:51', false, 1),
        (241, 'common-form:1:01d5d911-e9dd-11ee-b832-9eb3e287be9e', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 20:49:52', '1', '2024-03-24 20:49:52', false, 1),
        (242, 'common-form:2:0e493533-e9e7-11ee-b832-9eb3e287be9e', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-24 22:01:48', '1', '2024-03-24 22:01:48', false, 1),
        (243, 'oa_leave:6:7b44afa4-ea31-11ee-bb75-5e33ef4bb66f', 'e0d1eaff-ea2f-11ee-bb75-5e33ef4bb66f', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-25 06:54:34', '1', '2024-03-25 06:54:34', false, 1),
        (244, 'common-form:3:e4fde4e7-ea58-11ee-bb75-5e33ef4bb66f', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 11:36:42', '1', '2024-03-25 11:36:42', false, 1),
        (245, 'common-form:4:f410143b-ea58-11ee-bb75-5e33ef4bb66f', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 11:37:07', '1', '2024-03-25 11:37:07', false, 1),
        (246, 'oa_leave:7:7de7f1e6-ea75-11ee-b3de-d26fe03362ce', 'e0d1eaff-ea2f-11ee-bb75-5e33ef4bb66f', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-25 15:01:24', '1', '2024-03-25 15:01:24', false, 1),
        (247, 'common-form:5:9ba7c25a-ea75-11ee-b3de-d26fe03362ce', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 15:02:14', '1', '2024-03-25 15:02:14', false, 1),
        (248, 'oa_leave:8:8d386d5f-ea76-11ee-b3de-d26fe03362ce', 'e0d1eaff-ea2f-11ee-bb75-5e33ef4bb66f', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-03-25 15:08:59', '1', '2024-03-25 15:08:59', false, 1),
        (249, 'jiandan:30:25812eb2-ea8f-11ee-b3de-d26fe03362ce', '31b0d649-e8c8-11ee-afed-aa3623b10f85', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', '31', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 18:05:03', '1', '2024-03-25 18:05:03', false, 1),
        (250, 'common-form:6:10faaabb-eab4-11ee-b3de-d26fe03362ce', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 22:29:20', '1', '2024-03-25 22:29:20', false, 1),
        (251, 'common-form:7:130ac60f-eab4-11ee-b3de-d26fe03362ce', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-25 22:29:23', '1', '2024-03-25 22:29:23', false, 1),
        (252, 'common-form:8:6cd4e3df-eb56-11ee-b3de-d26fe03362ce', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 17:51:32', '1', '2024-03-26 17:51:32', false, 1),
        (253, 'common-form:9:2853c458-eb60-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:01:12', '1', '2024-03-26 19:01:12', false, 1),
        (254, 'common-form:10:492fa1e9-eb60-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:02:07', '1', '2024-03-26 19:02:07', false, 1),
        (255, 'common-form:11:7413b36c-eb60-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:03:19', '1', '2024-03-26 19:03:19', false, 1),
        (256, 'common-form:12:7d3a13e0-eb60-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:03:35', '1', '2024-03-26 19:03:35', false, 1),
        (257, 'common-form:13:370ab6b2-eb64-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:30:15', '1', '2024-03-26 19:30:15', false, 1),
        (258, 'common-form:14:6aa43f60-eb64-11ee-bdd0-965a2f407078', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:31:41', '1', '2024-03-26 19:31:41', false, 1),
        (259, 'common-form:15:dff9ac9c-eb65-11ee-8f77-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:42:08', '1', '2024-03-26 19:42:08', false, 1),
        (260, 'common-form:16:f8d53320-eb65-11ee-8f77-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:42:49', '1', '2024-03-26 19:42:49', false, 1),
        (261, 'common-form:17:65ca4431-eb66-11ee-8f77-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 19:45:52', '1', '2024-03-26 19:45:52', false, 1),
        (262, 'common-form:18:6ba8e1d3-eb68-11ee-8f77-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 20:00:21', '1', '2024-03-26 20:00:21', false, 1),
        (263, 'common-form:19:b0de9faf-eb68-11ee-ac74-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 20:02:17', '1', '2024-03-26 20:02:17', false, 1),
        (264, 'common-form:20:dcd92b40-eb68-11ee-ac74-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 20:03:31', '1', '2024-03-26 20:03:31', false, 1),
        (265, 'common-form:21:68b2dd38-eb69-11ee-94bc-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 20:07:26', '1', '2024-03-26 20:07:26', false, 1),
        (266, 'common-form:22:9390d538-eb69-11ee-94bc-8e54f03536f0', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 20:08:38', '1', '2024-03-26 20:08:38', false, 1),
        (267, 'common-form:23:dc057b15-eb7d-11ee-94eb-ca66e5b02be3', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-26 22:33:49', '1', '2024-03-26 22:33:49', false, 1),
        (268, 'heihei:1:06f60a22-ec15-11ee-94eb-ca66e5b02be3', 'abcd8d7d-ec14-11ee-94eb-ca66e5b02be3', 10, 'http://test.yudao.iocoder.cn/7926d23e751a3e6c3002d24386a826bb7e878ed6fb6be744bdfa57de9791a953.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-27 16:35:55', '1', '2024-03-27 16:35:55', false, 1),
        (269, 'heihei:2:b5e85669-ec2d-11ee-905c-5e14fc17cb1d', 'abcd8d7d-ec14-11ee-94eb-ca66e5b02be3', 10, 'http://test.yudao.iocoder.cn/7926d23e751a3e6c3002d24386a826bb7e878ed6fb6be744bdfa57de9791a953.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-27 19:32:36', '1', '2024-03-27 19:32:36', false, 1),
        (270, 'dingding:1:ebf8e7eb-eea0-11ee-9667-e2a265c77a9c', '6246f786-eea0-11ee-9667-e2a265c77a9c', 10, 'http://test.yudao.iocoder.cn/7c8923242e856d176a9054c671d4446c5b5e05c9ff316905b81b1d9062c47fc8.png', '123', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-03-30 22:22:22', '1', '2024-03-30 22:22:22', false, 1),
        (271, 'test-multi:1:fb1f8c36-2c71-11ef-a3cf-9eb4566c9ffb', 'a01741c1-2c71-11ef-a3cf-9eb4566c9ffb', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-06-17 14:22:33', '1', '2024-06-17 14:22:33', false, 1),
        (272, 'test-multi:2:fd3e749a-2c71-11ef-a3cf-9eb4566c9ffb', 'a01741c1-2c71-11ef-a3cf-9eb4566c9ffb', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-06-17 14:22:36', '1', '2024-06-17 14:22:36', false, 1),
        (273, 'test-multi:3:3118e1a3-2c73-11ef-be08-6200d05e9e35', 'a01741c1-2c71-11ef-a3cf-9eb4566c9ffb', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-06-17 14:31:13', '1', '2024-06-17 14:31:13', false, 1),
        (274, 'jiandan:1:d7d10d7c-46a3-11ef-9af1-ca5778fb00b9', 'ad764f47-46a3-11ef-9af1-ca5778fb00b9', 10, 'http://test.yudao.iocoder.cn/9c6f8897bf5b3baa457b056cdfd3edebb4c5def6b2f241267b5d6903606358af.png', '123', 20, null, null, null, '1', '2', '1', 0, true, null, null, '110', '2024-07-20 22:24:59', '110', '2024-07-20 22:24:59', false, 121),
        (275, 'oa_leave:9:3413581e-48b8-11ef-abfa-867f8d724d70', 'e0d1eaff-ea2f-11ee-bb75-5e33ef4bb66f', 10, 'http://test.yudao.iocoder.cn/a2b3603cf8797447c26a22f18256df94562d5e00bcbdc2a523098119c50a6efa.png', null, 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave/detail', '1', 0, true, null, null, '1', '2024-07-23 13:55:46', '1', '2024-07-23 13:55:46', false, 1),
        (276, 'common-form:24:8096981a-48b8-11ef-abfa-867f8d724d70', '749c937c-e9d8-11ee-b832-9eb3e287be9e', 10, 'http://test.yudao.iocoder.cn/9bb3c5ced2acdd50e303db8d719900ac20a3a8fab2f2002c56fc8da41b74c204.png', '通用表单的简单示例', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-07-23 13:57:54', '1', '2024-07-23 13:57:54', false, 1),
        (277, 'dingding-test:1:c8d05645-570e-11ef-9a2a-ca6b32ba0b65', '5a8cf34f-570e-11ef-9a2a-ca6b32ba0b65', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-10 19:50:48', '1', '2024-08-10 19:50:48', false, 1),
        (278, 'test-multi-02:1:ca9cc34b-5786-11ef-a7d0-ca5778fb00b9', 'ac088bd6-5786-11ef-a7d0-ca5778fb00b9', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-11 10:09:51', '1', '2024-08-11 10:09:51', false, 1),
        (279, 'test-multi-02:2:2775af2e-5787-11ef-a7d0-ca5778fb00b9', 'ac088bd6-5786-11ef-a7d0-ca5778fb00b9', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-11 10:12:27', '1', '2024-08-11 10:12:27', false, 1),
        (280, 'dingding-new-01:1:4ec71d17-5b14-11ef-8d8b-0216fe4e30bd', '150076d1-5b14-11ef-8d8b-0216fe4e30bd', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-15 22:40:25', '1', '2024-08-15 22:40:25', false, 1),
        (281, 'dingding-new-01:2:bd6da2c7-5be2-11ef-9e8e-0eb665c8c295', '150076d1-5b14-11ef-8d8b-0216fe4e30bd', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-16 23:18:07', '1', '2024-08-16 23:18:07', false, 1),
        (282, 'dingding-new-01:3:1fa9307b-5be3-11ef-9e8e-0eb665c8c295', '150076d1-5b14-11ef-8d8b-0216fe4e30bd', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-16 23:20:52', '1', '2024-08-16 23:20:52', false, 1),
        (283, 'dingding-new-02:1:9ced6afb-5c32-11ef-8920-6aa34c82345d', '81ca8c85-5c32-11ef-8920-6aa34c82345d', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', '你收呢！', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 08:49:52', '1', '2024-08-17 08:49:52', false, 1),
        (284, 'dingding-new-02:2:0594c7e2-5c45-11ef-ba72-da884e9ab97f', '81ca8c85-5c32-11ef-8920-6aa34c82345d', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', '你收呢！', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 11:01:39', '1', '2024-08-17 11:01:39', false, 1),
        (285, 'dingding-new-02:3:b1dc2c7b-5c45-11ef-b584-da884e9ab97f', '81ca8c85-5c32-11ef-8920-6aa34c82345d', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', '你收呢！', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 11:06:28', '1', '2024-08-17 11:06:28', false, 1),
        (286, 'dingding-new-02:4:cbe29e84-5c45-11ef-b584-da884e9ab97f', '81ca8c85-5c32-11ef-8920-6aa34c82345d', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', '你收呢！', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 11:07:12', '1', '2024-08-17 11:07:12', false, 1),
        (287, 'dingding-new-02:5:11743139-5c49-11ef-a949-da884e9ab97f', '81ca8c85-5c32-11ef-8920-6aa34c82345d', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', '你收呢！', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 11:30:37', '1', '2024-08-17 11:30:37', false, 1),
        (288, 'dingding-new-03:1:52e8ed81-5c5f-11ef-b68e-fada804b1506', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 14:09:55', '1', '2024-08-17 14:09:55', false, 1),
        (289, 'dingding-new-03:2:71f8bda4-5c5f-11ef-b68e-fada804b1506', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 14:10:48', '1', '2024-08-17 14:10:48', false, 1),
        (290, 'dingding-new-03:3:7906ec78-5c5f-11ef-b68e-fada804b1506', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 14:10:59', '1', '2024-08-17 14:10:59', false, 1),
        (291, 'dingding-new-03:4:e4448c76-5c63-11ef-801e-ee42d61a19f3', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 14:42:37', '1', '2024-08-17 14:42:37', false, 1),
        (292, 'dingding-new-03:5:85f5e50b-5c6d-11ef-8d6e-aed50561faa5', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 15:51:34', '1', '2024-08-17 15:51:34', false, 1),
        (293, 'dingding-new-03:6:42a6615b-5c6f-11ef-b472-aed50561faa5', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 16:04:00', '1', '2024-08-17 16:04:00', false, 1),
        (294, 'dingding-new-03:7:be066588-5c6f-11ef-9238-aed50561faa5', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 16:07:27', '1', '2024-08-17 16:07:27', false, 1),
        (295, 'dingding-new-03:8:684002ca-5c71-11ef-84c7-e2daa89f1c3f', '272cf19b-5c5f-11ef-b68e-fada804b1506', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 16:19:22', '1', '2024-08-17 16:19:22', false, 1),
        (296, 'dingding-new-04:1:0ca99988-5c82-11ef-bedc-bec2e0c2f11f', 'a3a969df-5c81-11ef-921d-bec2e0c2f11f', 10, 'http://test.yudao.iocoder.cn/c659aa14a791d94ba75e1d2d60d41aa62903e5a6da5fb8f7df3e71532ee69fee.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 18:18:30', '1', '2024-08-17 18:18:30', false, 1),
        (297, 'dingding-new-05:1:be1300a8-5c9a-11ef-a8e4-92f37d84469a', '961b11a2-5c9a-11ef-a8e4-92f37d84469a', 10, 'http://test.yudao.iocoder.cn/e1c52473ec0716ecfb18bf2c2b8685883a3f4b511e54dbef2d39920afaa4f4ef.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 21:15:16', '1', '2024-08-17 21:15:16', false, 1),
        (298, 'dingding-new-05:2:dc369c07-5c9a-11ef-a8e4-92f37d84469a', '961b11a2-5c9a-11ef-a8e4-92f37d84469a', 10, 'http://test.yudao.iocoder.cn/e1c52473ec0716ecfb18bf2c2b8685883a3f4b511e54dbef2d39920afaa4f4ef.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 21:16:06', '1', '2024-08-17 21:16:06', false, 1),
        (299, 'dingding-new-05:3:f50f3c66-5c9a-11ef-a8e4-92f37d84469a', '961b11a2-5c9a-11ef-a8e4-92f37d84469a', 10, 'http://test.yudao.iocoder.cn/e1c52473ec0716ecfb18bf2c2b8685883a3f4b511e54dbef2d39920afaa4f4ef.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-17 21:16:48', '1', '2024-08-17 21:16:48', false, 1),
        (300, 'simple-01:1:b0cbe161-61de-11ef-abc5-ca1dd2e8f300', '917fa48b-61de-11ef-abc5-ca1dd2e8f300', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-24 14:04:15', '1', '2024-08-24 14:04:15', false, 1),
        (301, 'simple-02:1:6f3a5cf8-61e7-11ef-8927-c646985e83ad', '57b5daf2-61e7-11ef-8927-c646985e83ad', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-24 15:06:50', '1', '2024-08-24 15:06:50', false, 1),
        (302, 'simple-02:2:e3ddd385-61e7-11ef-8927-c646985e83ad', '57b5daf2-61e7-11ef-8927-c646985e83ad', 10, 'http://test.yudao.iocoder.cn/7401c394a43280732e6aaa715fbfefadc33eeb8fab8f45f6b53f1acf6b22ae29.png', null, 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '1', 0, true, null, null, '1', '2024-08-24 15:10:06', '1', '2024-08-24 15:10:06', false, 1),
        (303, 'simple-03:1:ffd041ac-6395-11ef-9c91-e6bb11cfb2c8', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '1', 0, true, null, null, '1', '2024-08-26 18:28:57', '1', '2024-08-26 18:28:57', false, 1),
        (304, 'simple-03:2:acdc2fca-6396-11ef-9467-9e8b8de3d574', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '0', 0, true, null, null, '1', '2024-08-26 18:33:47', '1', '2024-08-26 18:33:47', false, 1),
        (305, 'simple-03:3:67b17c0f-71c7-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"checkbox\\",\\"field\\":\\"Fo2b64a6nn6yw\\",\\"title\\":\\"多选框\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"选项1\\",\\"value\\":1},{\\"label\\":\\"选项2\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"checkbox\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '0', 0, true, null, null, '1', '2024-09-13 19:57:52', '1', '2024-09-13 19:57:52', false, 1),
        (306, 'simple-03:4:25e2dcdd-71d2-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"cascader\\",\\"field\\":\\"Fafp64a7f65sr\\",\\"title\\":\\"级联选择器\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"props\\":{\\"options\\":[{\\"label\\":\\"选项1\\",\\"value\\":1,\\"children\\":[]},{\\"label\\":\\"选项2\\",\\"value\\":2,\\"children\\":[]}]},\\"_fc_drag_tag\\":\\"cascader\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, false, null, null, '1', '2024-09-13 21:14:46', '1', '2024-09-13 21:14:46', false, 1),
        (307, 'simple-03:5:39b9c031-71d2-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"cascader\\",\\"field\\":\\"Fafp64a7f65sr\\",\\"title\\":\\"级联选择器\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"props\\":{\\"options\\":[{\\"label\\":\\"选项1\\",\\"value\\":1,\\"children\\":[]},{\\"label\\":\\"选项2\\",\\"value\\":2,\\"children\\":[]}]},\\"_fc_drag_tag\\":\\"cascader\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, false, null, null, '1', '2024-09-13 21:15:20', '1', '2024-09-13 21:15:20', false, 1),
        (308, 'simple-03:6:3f8c13a5-71d2-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"cascader\\",\\"field\\":\\"Fafp64a7f65sr\\",\\"title\\":\\"级联选择器\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"props\\":{\\"options\\":[{\\"label\\":\\"选项1\\",\\"value\\":1,\\"children\\":[]},{\\"label\\":\\"选项2\\",\\"value\\":2,\\"children\\":[]}]},\\"_fc_drag_tag\\":\\"cascader\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, true, null, null, '1', '2024-09-13 21:15:30', '1', '2024-09-13 21:15:30', false, 1),
        (309, 'simple-03:7:74db9347-71d3-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"cascader\\",\\"field\\":\\"Fafp64a7f65sr\\",\\"title\\":\\"级联选择器\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"props\\":{\\"options\\":[{\\"label\\":\\"选项1\\",\\"value\\":1,\\"children\\":[{\\"label\\":\\"选项11\\",\\"value\\":11,\\"children\\":[]},{\\"label\\":\\"选项12\\",\\"value\\":12,\\"children\\":[]}]},{\\"label\\":\\"选项2\\",\\"value\\":2,\\"children\\":[]}]},\\"_fc_drag_tag\\":\\"cascader\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, true, null, null, '1', '2024-09-13 21:24:08', '1', '2024-09-13 21:24:08', false, 1),
        (310, 'simple-03:8:f46606f9-71d3-11ef-ac99-5621c86b7390', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 28, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"tree\\",\\"field\\":\\"Flm564a7jrfah\\",\\"title\\":\\"树形控件\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"props\\":{\\"props\\":{\\"label\\":\\"label\\"},\\"showCheckbox\\":true,\\"nodeKey\\":\\"id\\",\\"data\\":[{\\"label\\":\\"选项1\\",\\"id\\":1,\\"children\\":[{\\"label\\":\\"选项11\\",\\"id\\":11,\\"children\\":[]},{\\"label\\":\\"选项12\\",\\"id\\":12,\\"children\\":[]}]},{\\"label\\":\\"选项2\\",\\"id\\":2,\\"children\\":[]}]},\\"_fc_drag_tag\\":\\"tree\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, true, null, null, '1', '2024-09-13 21:27:42', '1', '2024-09-13 21:27:42', false, 1),
        (311, 'simple-03:9:54f0c53d-74c1-11ef-b607-56c19462ac4b', 'a10355c1-6392-11ef-93ff-6e48fd3e48cc', 10, 'http://test.yudao.iocoder.cn/a8eb4341b2c88afb56f7b4d61826a55b9228e06e772cb140a411b15f9f235be0.png', 'ee', 10, 29, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"UploadFile\\",\\"field\\":\\"6998d577-3e09-45db-945a-e38ae063070c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadImg\\",\\"field\\":\\"69111c0e-e00a-4968-a363-dd8270d077a0\\",\\"title\\":\\"单图上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadImg\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadImgs\\",\\"field\\":\\"051b5d49-bc02-44e5-bed1-b85d56a979ff\\",\\"title\\":\\"多图上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadImgs\\",\\"hidden\\":false,\\"display\\":true}"]', 'x', 'y', '', 0, true, null, null, '1', '2024-09-17 14:51:57', '1', '2024-09-17 14:51:57', false, 1),
        (312, 'test-multi:4:5052448b-74c6-11ef-83e6-8a1db91ea8d5', 'a01741c1-2c71-11ef-a3cf-9eb4566c9ffb', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '', 0, true, null, null, '1', '2024-09-17 15:27:37', '1', '2024-09-17 15:27:37', false, 1),
        (313, 'test-multi-02:3:fbae7f56-7f21-11ef-bdea-ca5778fb00b9', 'ac088bd6-5786-11ef-a7d0-ca5778fb00b9', 10, 'http://test.yudao.iocoder.cn/2a124ba5743f9572fcbd2718a64ba599618c96ddba6c7391ad35906cd3f37f94.png', null, 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '', 0, true, null, null, '1', '2024-09-30 19:49:01', '1', '2024-09-30 19:49:01', false, 1),
        (314, 'test-duoren:1:9276258a-7fd6-11ef-bf92-323a765bd653', '6335bdd5-7fd6-11ef-bf92-323a765bd653', 10, 'http://test.yudao.iocoder.cn/84defd9f8ca220e357894a61c781b16950849b481752d4f76b797188f7286bd8.png', '321', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', null, null, '', 0, true, null, null, '1', '2024-10-01 17:21:43', '1', '2024-10-01 17:21:43', false, 1),
        (315, 'new-key:1:f45cd1cd-8138-11ef-98ed-0ad1a6b0a8a7', 'cd5d2578-8138-11ef-98ed-0ad1a6b0a8a7', 10, 'http://test.yudao.iocoder.cn/96f06d6c23c0207ffd03d71bd020ea1550a785441f95b9de4cf3765a202a68c8.png', '', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '', 0, true, null, null, '1', '2024-10-03 11:38:29', '1', '2024-10-03 11:38:29', false, 1),
        (316, 'new-key:2:6f340a88-816b-11ef-980e-c6742a20eb3e', 'cd5d2578-8138-11ef-98ed-0ad1a6b0a8a7', 10, 'http://test.yudao.iocoder.cn/96f06d6c23c0207ffd03d71bd020ea1550a785441f95b9de4cf3765a202a68c8.png', '', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '', 0, true, '100', '1,100,103', '1', '2024-10-03 17:39:50', '1', '2024-10-03 17:39:50', false, 1),
        (317, 'dingding-demo:1:32856ac4-8200-11ef-80a2-d693a04a0735', '1d7d4fdf-81ff-11ef-abac-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/9545f9b6ac86779f1b20337b159a85a46f05c6be68df2a31d707f2d7e61f6f54.png', '', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_b967abd5-1238-45de-afee-2eb41ae64b61","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"1"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"1"},{"field":"type","title":"请假类型","permission":"1"},{"field":"reason","title":"请假原因","permission":"1"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"2"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"2"},{"field":"type","title":"请假类型","permission":"2"},{"field":"reason","title":"请假原因","permission":"2"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 11:24:43', '1', '2024-10-04 11:24:43', false, 1),
        (318, 'dingding-demo:2:f2654a62-8200-11ef-80a2-d693a04a0735', '1d7d4fdf-81ff-11ef-abac-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/9545f9b6ac86779f1b20337b159a85a46f05c6be68df2a31d707f2d7e61f6f54.png', '', 10, 27, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"datePicker\\",\\"field\\":\\"startTime\\",\\"title\\":\\"开始时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"datePicker\\",\\"field\\":\\"Fm9i1onr8v6n68\\",\\"title\\":\\"结束时间\\",\\"info\\":\\"\\",\\"$required\\":true,\\"_fc_drag_tag\\":\\"datePicker\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"select\\",\\"field\\":\\"type\\",\\"title\\":\\"请假类型\\",\\"info\\":\\"\\",\\"effect\\":{\\"fetch\\":\\"\\"},\\"$required\\":false,\\"options\\":[{\\"label\\":\\"事假\\",\\"value\\":1},{\\"label\\":\\"年假\\",\\"value\\":2}],\\"_fc_drag_tag\\":\\"select\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"reason\\",\\"title\\":\\"请假原因\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"rows\\":0,\\"type\\":\\"textarea\\"},\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"UploadFile\\",\\"field\\":\\"c982ea4c-7bbd-40d3-9f58-4e6409feed7c\\",\\"title\\":\\"文件上传\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"UploadFile\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_b967abd5-1238-45de-afee-2eb41ae64b61","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"Activity_c6a42fdd-5e2d-4670-adf8-9c6b0763aaeb","type":11,"name":"审批人","showText":"指定成员：芋道源码,芋道,源码,测试号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"100,104,103,1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"1"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"1"},{"field":"type","title":"请假类型","permission":"1"},{"field":"reason","title":"请假原因","permission":"1"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"1"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"1"},{"field":"type","title":"请假类型","permission":"1"},{"field":"reason","title":"请假原因","permission":"1"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"2"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"2"},{"field":"type","title":"请假类型","permission":"2"},{"field":"reason","title":"请假原因","permission":"2"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 11:30:05', '1', '2024-10-04 11:30:05', false, 1),
        (319, 'dingding-demo:3:fca032bb-8201-11ef-80a2-d693a04a0735', '1d7d4fdf-81ff-11ef-abac-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/9545f9b6ac86779f1b20337b159a85a46f05c6be68df2a31d707f2d7e61f6f54.png', '', 10, 30, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Famz1p93tieo6r\\",\\"title\\":\\"输入框1\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdgk1p93tiyqoo\\",\\"title\\":\\"输入框2\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F73v1p93tiyehp\\",\\"title\\":\\"输入框3\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fcpi1p93tiy2aq\\",\\"title\\":\\"输入框4\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fut41p93tixq3r\\",\\"title\\":\\"输入框5\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fbi21p93tixej0\\",\\"title\\":\\"输入框6\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fwbk1p93tix299\\",\\"title\\":\\"输入框7\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F4zu1p93tiwoze\\",\\"title\\":\\"输入框8\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffuu1p93tiwc8z\\",\\"title\\":\\"输入框9\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F6zg1p93tivzz8\\",\\"title\\":\\"输入框10\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fryb1p93tivn8t\\",\\"title\\":\\"输入框11\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F5p51p93tivb1u\\",\\"title\\":\\"输入框12\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fq6f1p93tiuyuv\\",\\"title\\":\\"输入框13\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fvrc1p93tiuna4\\",\\"title\\":\\"输入框14\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F44d1p93tiub5x\\",\\"title\\":\\"输入框15\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fans1p93titxta\\",\\"title\\":\\"输入框16\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fkvp1p93titkxb\\",\\"title\\":\\"输入框17\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffhi1p93tit7yk\\",\\"title\\":\\"输入框18\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fug81p93tis24x\\",\\"title\\":\\"输入框19\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_b967abd5-1238-45de-afee-2eb41ae64b61","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"Activity_c6a42fdd-5e2d-4670-adf8-9c6b0763aaeb","type":11,"name":"审批人","showText":"指定成员：芋道源码,芋道,源码,测试号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"100,104,103,1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"1"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"1"},{"field":"type","title":"请假类型","permission":"1"},{"field":"reason","title":"请假原因","permission":"1"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"1"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"1"},{"field":"type","title":"请假类型","permission":"1"},{"field":"reason","title":"请假原因","permission":"1"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"2"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"2"},{"field":"type","title":"请假类型","permission":"2"},{"field":"reason","title":"请假原因","permission":"2"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 11:37:32', '1', '2024-10-04 11:37:32', false, 1),
        (320, 'dingding-test01:1:846e8448-8204-11ef-80a2-d693a04a0735', '63f2a112-8204-11ef-80a2-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/633159e152c914578b6eb05fd35d9204cce50f8f183899a8d93a82ed9a818316.png', '', 10, 25, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fgjo62bltzzxt\\",\\"title\\":\\"新的表单\\",\\"info\\":\\"\\",\\"$required\\":\\"新的表单，必须填写\\",\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_e49a1ccc-1a43-4690-b455-b627896fc3c3","type":11,"name":"各个领导的审批","showText":"指定成员：芋道源码,芋道,源码,测试号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1,100,103,104","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 11:55:39', '1', '2024-10-04 11:55:39', false, 1),
        (321, 'dingding-test01:2:8c9946ac-8204-11ef-80a2-d693a04a0735', '63f2a112-8204-11ef-80a2-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/633159e152c914578b6eb05fd35d9204cce50f8f183899a8d93a82ed9a818316.png', '', 10, 25, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fgjo62bltzzxt\\",\\"title\\":\\"新的表单\\",\\"info\\":\\"\\",\\"$required\\":\\"新的表单，必须填写\\",\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_e49a1ccc-1a43-4690-b455-b627896fc3c3","type":11,"name":"各个领导的审批","showText":"指定成员：芋道源码,源码,测试号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1,103,104","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 11:55:52', '1', '2024-10-04 11:55:52', false, 1),
        (322, 'dingding-test01:3:a12112c9-8223-11ef-80a2-d693a04a0735', '63f2a112-8204-11ef-80a2-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/633159e152c914578b6eb05fd35d9204cce50f8f183899a8d93a82ed9a818316.png', '', 10, 25, '{"form":{"labelPosition":"right","size":"default","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fgjo62bltzzxt\\",\\"title\\":\\"新的表单\\",\\"info\\":\\"\\",\\"$required\\":\\"新的表单，必须填写\\",\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_e49a1ccc-1a43-4690-b455-b627896fc3c3","type":11,"name":"各个领导的审批","showText":"指定成员：芋道源码,源码,测试号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1,103,104","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"回退","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"Fgjo62bltzzxt","title":"新的表单","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-04 15:38:21', '1', '2024-10-04 15:38:21', false, 1),
        (323, 'test-bohui-second:1:6bd3bf48-8533-11ef-a5d2-e606a327ad6d', '22d05743-8533-11ef-a5d2-e606a327ad6d', 10, 'http://test.yudao.iocoder.cn/633159e152c914578b6eb05fd35d9204cce50f8f183899a8d93a82ed9a818316.png', '', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '', 0, true, '', '1', '1', '2024-10-08 13:08:57', '1', '2024-10-08 13:08:57', false, 1),
        (324, 'test-bpmn-multi:1:547238b7-8642-11ef-85c1-feff1922d8e5', '43bedc82-8642-11ef-85c1-feff1922d8e5', 10, 'http://test.yudao.iocoder.cn/633159e152c914578b6eb05fd35d9204cce50f8f183899a8d93a82ed9a818316.png', '312312', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '', 0, true, '', '1', '1', '2024-10-09 21:28:11', '1', '2024-10-09 21:28:11', false, 1),
        (325, 'dingding-demo:4:e4a26d53-8de2-11ef-9422-2ae51d8919f0', '1d7d4fdf-81ff-11ef-abac-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/9545f9b6ac86779f1b20337b159a85a46f05c6be68df2a31d707f2d7e61f6f54.png', '', 10, 30, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Famz1p93tieo6r\\",\\"title\\":\\"输入框1\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdgk1p93tiyqoo\\",\\"title\\":\\"输入框2\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F73v1p93tiyehp\\",\\"title\\":\\"输入框3\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fcpi1p93tiy2aq\\",\\"title\\":\\"输入框4\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fut41p93tixq3r\\",\\"title\\":\\"输入框5\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fbi21p93tixej0\\",\\"title\\":\\"输入框6\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fwbk1p93tix299\\",\\"title\\":\\"输入框7\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F4zu1p93tiwoze\\",\\"title\\":\\"输入框8\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffuu1p93tiwc8z\\",\\"title\\":\\"输入框9\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F6zg1p93tivzz8\\",\\"title\\":\\"输入框10\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fryb1p93tivn8t\\",\\"title\\":\\"输入框11\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F5p51p93tivb1u\\",\\"title\\":\\"输入框12\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fq6f1p93tiuyuv\\",\\"title\\":\\"输入框13\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fvrc1p93tiuna4\\",\\"title\\":\\"输入框14\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F44d1p93tiub5x\\",\\"title\\":\\"输入框15\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fans1p93titxta\\",\\"title\\":\\"输入框16\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fkvp1p93titkxb\\",\\"title\\":\\"输入框17\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffhi1p93tit7yk\\",\\"title\\":\\"输入框18\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fug81p93tis24x\\",\\"title\\":\\"输入框19\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_0c28d217-e0c2-47fd-9518-901367382fe3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_7b768e5e-8d06-4b80-8919-22732478016c","type":50,"name":"条件1","showText":"表达式：1=1","childNode":{"id":"Activity_5e2b7323-7546-4115-84eb-c022438a0706","type":11,"name":"审批人","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Famz1p93tieo6r","title":"输入框1","permission":"1"},{"field":"Fdgk1p93tiyqoo","title":"输入框2","permission":"1"},{"field":"F73v1p93tiyehp","title":"输入框3","permission":"1"},{"field":"Fcpi1p93tiy2aq","title":"输入框4","permission":"1"},{"field":"Fut41p93tixq3r","title":"输入框5","permission":"1"},{"field":"Fbi21p93tixej0","title":"输入框6","permission":"1"},{"field":"Fwbk1p93tix299","title":"输入框7","permission":"1"},{"field":"F4zu1p93tiwoze","title":"输入框8","permission":"1"},{"field":"Ffuu1p93tiwc8z","title":"输入框9","permission":"1"},{"field":"F6zg1p93tivzz8","title":"输入框10","permission":"1"},{"field":"Fryb1p93tivn8t","title":"输入框11","permission":"1"},{"field":"F5p51p93tivb1u","title":"输入框12","permission":"1"},{"field":"Fq6f1p93tiuyuv","title":"输入框13","permission":"1"},{"field":"Fvrc1p93tiuna4","title":"输入框14","permission":"1"},{"field":"F44d1p93tiub5x","title":"输入框15","permission":"1"},{"field":"Fans1p93titxta","title":"输入框16","permission":"1"},{"field":"Fkvp1p93titkxb","title":"输入框17","permission":"1"},{"field":"Ffhi1p93tit7yk","title":"输入框18","permission":"1"},{"field":"Fug81p93tis24x","title":"输入框19","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":1,"conditionExpression":"1=1","defaultFlow":false},{"id":"Flow_44b36420-1005-4427-bfab-554b66f760a7","type":50,"name":"其它情况","showText":"其它情况进入此流程","defaultFlow":true}]},"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"2"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"2"},{"field":"type","title":"请假类型","permission":"2"},{"field":"reason","title":"请假原因","permission":"2"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 14:25:11', '1', '2024-10-19 14:25:11', false, 1),
        (326, 'dingding-demo:5:76b70e85-8de5-11ef-bb44-469a9b560ac7', '1d7d4fdf-81ff-11ef-abac-d693a04a0735', 20, 'http://test.yudao.iocoder.cn/9545f9b6ac86779f1b20337b159a85a46f05c6be68df2a31d707f2d7e61f6f54.png', '', 10, 30, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Famz1p93tieo6r\\",\\"title\\":\\"输入框1\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdgk1p93tiyqoo\\",\\"title\\":\\"输入框2\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F73v1p93tiyehp\\",\\"title\\":\\"输入框3\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fcpi1p93tiy2aq\\",\\"title\\":\\"输入框4\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fut41p93tixq3r\\",\\"title\\":\\"输入框5\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fbi21p93tixej0\\",\\"title\\":\\"输入框6\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fwbk1p93tix299\\",\\"title\\":\\"输入框7\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F4zu1p93tiwoze\\",\\"title\\":\\"输入框8\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffuu1p93tiwc8z\\",\\"title\\":\\"输入框9\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F6zg1p93tivzz8\\",\\"title\\":\\"输入框10\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fryb1p93tivn8t\\",\\"title\\":\\"输入框11\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F5p51p93tivb1u\\",\\"title\\":\\"输入框12\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fq6f1p93tiuyuv\\",\\"title\\":\\"输入框13\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fvrc1p93tiuna4\\",\\"title\\":\\"输入框14\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"F44d1p93tiub5x\\",\\"title\\":\\"输入框15\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fans1p93titxta\\",\\"title\\":\\"输入框16\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fkvp1p93titkxb\\",\\"title\\":\\"输入框17\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Ffhi1p93tit7yk\\",\\"title\\":\\"输入框18\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}","{\\"type\\":\\"input\\",\\"field\\":\\"Fug81p93tis24x\\",\\"title\\":\\"输入框19\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_0c28d217-e0c2-47fd-9518-901367382fe3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_7b768e5e-8d06-4b80-8919-22732478016c","type":50,"name":"条件1","showText":"表达式：1=1","childNode":{"id":"Activity_5e2b7323-7546-4115-84eb-c022438a0706","type":11,"name":"审批人","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Famz1p93tieo6r","title":"输入框1","permission":"1"},{"field":"Fdgk1p93tiyqoo","title":"输入框2","permission":"1"},{"field":"F73v1p93tiyehp","title":"输入框3","permission":"1"},{"field":"Fcpi1p93tiy2aq","title":"输入框4","permission":"1"},{"field":"Fut41p93tixq3r","title":"输入框5","permission":"1"},{"field":"Fbi21p93tixej0","title":"输入框6","permission":"1"},{"field":"Fwbk1p93tix299","title":"输入框7","permission":"1"},{"field":"F4zu1p93tiwoze","title":"输入框8","permission":"1"},{"field":"Ffuu1p93tiwc8z","title":"输入框9","permission":"1"},{"field":"F6zg1p93tivzz8","title":"输入框10","permission":"1"},{"field":"Fryb1p93tivn8t","title":"输入框11","permission":"1"},{"field":"F5p51p93tivb1u","title":"输入框12","permission":"1"},{"field":"Fq6f1p93tiuyuv","title":"输入框13","permission":"1"},{"field":"Fvrc1p93tiuna4","title":"输入框14","permission":"1"},{"field":"F44d1p93tiub5x","title":"输入框15","permission":"1"},{"field":"Fans1p93titxta","title":"输入框16","permission":"1"},{"field":"Fkvp1p93titkxb","title":"输入框17","permission":"1"},{"field":"Ffhi1p93tit7yk","title":"输入框18","permission":"1"},{"field":"Fug81p93tis24x","title":"输入框19","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":1,"conditionExpression":"1=1","defaultFlow":false},{"id":"Flow_44b36420-1005-4427-bfab-554b66f760a7","type":50,"name":"其它情况","showText":"其它情况进入此流程","defaultFlow":true}]},"fieldsPermission":[{"field":"startTime","title":"开始时间","permission":"2"},{"field":"Fm9i1onr8v6n68","title":"结束时间","permission":"2"},{"field":"type","title":"请假类型","permission":"2"},{"field":"reason","title":"请假原因","permission":"2"},{"field":"c982ea4c-7bbd-40d3-9f58-4e6409feed7c","title":"文件上传","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 14:43:35', '1', '2024-10-19 14:43:35', false, 1),
        (327, 'dd-test01:1:b4285add-8de7-11ef-8d94-86849c485c24', '859d1257-8de7-11ef-8d94-86849c485c24', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '仿钉钉设计器', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_20dceaa5-8743-479d-9973-7466ea49c9d3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_c9854ec9-247d-40c8-9b42-22d0d3ca9227","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_2abeedeb-3631-4735-af92-cfd96e79192e","type":11,"name":"审批人（条件1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"Fjph60d8vlrl9","rightSide":"1"}]}]}},{"id":"Flow_9ab1b69e-f579-47a8-8f04-a1653ae28903","type":50,"name":"其它情况","showText":"其它情况进入此流程","childNode":{"id":"Activity_3e938a57-ab55-4d41-a690-7f8b7842e090","type":11,"name":"审批人（条件2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 14:59:37', '1', '2024-10-19 14:59:37', false, 1),
        (328, 'dd-test01:2:d290ae57-8dec-11ef-ab5b-0e429fa854dc', '859d1257-8de7-11ef-8d94-86849c485c24', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '仿钉钉设计器', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_20dceaa5-8743-479d-9973-7466ea49c9d3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_c9854ec9-247d-40c8-9b42-22d0d3ca9227","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_2abeedeb-3631-4735-af92-cfd96e79192e","type":11,"name":"审批人（条件1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"Fjph60d8vlrl9","rightSide":"1"}]}]}},{"id":"Flow_9ab1b69e-f579-47a8-8f04-a1653ae28903","type":50,"name":"其它情况","showText":"其它情况进入此流程","childNode":{"id":"Activity_3e938a57-ab55-4d41-a690-7f8b7842e090","type":11,"name":"审批人（条件2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 15:36:16', '1', '2024-10-19 15:36:16', false, 1),
        (329, 'dd-test01:3:3dc35ae2-8e08-11ef-81c9-6a7b77f5ecdf', '859d1257-8de7-11ef-8d94-86849c485c24', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '仿钉钉设计器', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_20dceaa5-8743-479d-9973-7466ea49c9d3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_c9854ec9-247d-40c8-9b42-22d0d3ca9227","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_2abeedeb-3631-4735-af92-cfd96e79192e","type":11,"name":"审批人（条件1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"Fjph60d8vlrl9","rightSide":"1"}]}]}},{"id":"Flow_9ab1b69e-f579-47a8-8f04-a1653ae28903","type":50,"name":"其它情况","showText":"其它情况进入此流程","childNode":{"id":"Activity_3e938a57-ab55-4d41-a690-7f8b7842e090","type":11,"name":"审批人（条件2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 18:52:32', '1', '2024-10-19 18:52:32', false, 1),
        (330, 'dd-test01:4:54ed8706-8e08-11ef-81c9-6a7b77f5ecdf', '859d1257-8de7-11ef-8d94-86849c485c24', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '仿钉钉设计器', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_20dceaa5-8743-479d-9973-7466ea49c9d3","type":51,"name":"条件分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_c9854ec9-247d-40c8-9b42-22d0d3ca9227","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_2abeedeb-3631-4735-af92-cfd96e79192e","type":11,"name":"审批人（条件1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"Fjph60d8vlrl9","rightSide":"1"}]}]}},{"id":"Flow_9ab1b69e-f579-47a8-8f04-a1653ae28903","type":50,"name":"其它情况","showText":"其它情况进入此流程","childNode":{"id":"Activity_3e938a57-ab55-4d41-a690-7f8b7842e090","type":11,"name":"审批人（条件2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"回退","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2024-10-19 18:53:11', '1', '2024-10-19 18:53:11', false, 1),
        (331, 'new-form-test:1:8f685bb5-8e7a-11ef-b032-92bb1f99c49d', '7be9aa80-8e7a-11ef-b032-92bb1f99c49d', 10, 'http://test.yudao.iocoder.cn/b36b69a9389417970bcaa546d363acfd4167150666c2d02a8ff271afd9573b0d.png', '321321', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '', 0, true, '', '1', '1', '2024-10-20 08:30:52', '1', '2024-10-20 08:30:52', false, 1),
        (332, 'new-form-test:2:38dbd8a6-8e7e-11ef-b032-92bb1f99c49d', '7be9aa80-8e7a-11ef-b032-92bb1f99c49d', 10, 'http://test.yudao.iocoder.cn/b36b69a9389417970bcaa546d363acfd4167150666c2d02a8ff271afd9573b0d.png', '321321', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '', 0, true, '', '1', '1', '2024-10-20 08:57:04', '1', '2024-10-20 08:57:04', false, 1),
        (333, 'test-001:1:6daba54f-8e83-11ef-b032-92bb1f99c49d', '5af01d09-8e83-11ef-b032-92bb1f99c49d', 20, 'http://test.yudao.iocoder.cn/7f57be8f3808693f9a5fa78277c6f4ec55cc69f6b61760683498d7112f0dbed2.jpg', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_594b7ff9-3fee-43f9-97c9-900c1e35d8d7","type":11,"name":"审批人","showText":"指定成员：芋道源码,芋道","childNode":{"id":"Activity_25061c33-55af-480e-ba62-ea57c0cb9dee","type":11,"name":"审批人","showText":"指定成员：芋道源码,芋道,源码,测试号,新对象,hr 小姐姐","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1,100,103,104,112,114","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1,100","approveType":1,"approveMethod":2,"approveRatio":100,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-20 09:34:20', '1', '2024-10-20 09:34:20', false, 1),
        (334, 'test-001:2:015b2ce3-8e8d-11ef-b032-92bb1f99c49d', '5af01d09-8e83-11ef-b032-92bb1f99c49d', 20, 'http://test.yudao.iocoder.cn/7f57be8f3808693f9a5fa78277c6f4ec55cc69f6b61760683498d7112f0dbed2.jpg', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_594b7ff9-3fee-43f9-97c9-900c1e35d8d7","type":11,"name":"主管审批","showText":"指定成员：芋道源码,芋道","childNode":{"id":"Activity_25061c33-55af-480e-ba62-ea57c0cb9dee","type":11,"name":"财务审批","showText":"指定成员：芋道源码,芋道,源码,测试号,新对象,hr 小姐姐","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1,100,103,104,112,114","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1,100","approveType":1,"approveMethod":2,"approveRatio":100,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-20 10:42:54', '1', '2024-10-20 10:42:54', false, 1),
        (335, 'a-fuza-liucheng:1:dd477671-8f92-11ef-947c-ba5e239a6eb4', '4601b953-8f92-11ef-947c-ba5e239a6eb4', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '333', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","childNode":{"id":"Activity_8cf244ab-06dd-482a-a450-9d27b95e9329","type":11,"name":"审批人1","showText":"指定成员：芋道源码","childNode":{"id":"GateWay_f2d542fa-6a3f-467a-ae44-0dd82baf2074","type":51,"name":"条件分支","childNode":{"id":"GateWay_163d43e0-d611-4a98-ac64-6ff3a5ccbcd7","type":52,"name":"并行分支","childNode":{"id":"GateWay_83b50563-d1be-4285-92c7-a1835609af94","type":53,"name":"包容分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_b82eee37-6334-40ff-8905-0675fbcdf890","type":50,"name":"包容条件1","showText":"(输入框 等于 2 ) ","childNode":{"id":"Activity_da31d23c-5694-40d5-b947-6380a9a2b5a9","type":11,"name":"审批人41","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"2"}]}]}},{"id":"Flow_48dcd8b2-c479-463c-9102-82182295ff60","type":50,"name":"包容条件2","showText":"(输入框 等于 3 ) ","childNode":{"id":"Activity_cebf2a5f-de9b-43fa-939d-ca7d64f40237","type":11,"name":"审批人42","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionNodes":[],"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"3"}]}]}},{"id":"Flow_99e16653-b0b1-4494-956c-5b56c5790eaa","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_7594cc43-bf07-4521-bb21-e2b610cf6dbc","type":11,"name":"审批人43","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"conditionNodes":[{"id":"Flow_f43d89ba-0e97-40aa-82c9-67558edf0376","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_7617020f-7bb0-4332-acdf-602234b46137","type":11,"name":"审批人31","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_c24d111d-e11c-4cd8-9310-d9eb81420739","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_c7881bce-0da2-4b70-9de3-ba4b6245c898","type":11,"name":"审批人32","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]},"conditionNodes":[{"id":"Flow_a1556f3e-58c5-4db0-a7eb-b583de1e2c70","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_6bd27fd4-25fc-42c1-beac-458123b72a68","type":11,"name":"审批人21","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"1"}]}]}},{"id":"Flow_d7fb79fd-175b-4b30-8a44-d8c67fcd5e80","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_405d08c8-93ce-4f3c-a5af-b022e1a36a10","type":11,"name":"审批人22","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}', 0, true, '', '1', '1', '2024-10-21 17:57:21', '1', '2024-10-21 17:57:21', false, 1),
        (336, '61f69eae-8fa6-11ef-9532-fa47f0b26dd4', '52b23bc8-8fa6-11ef-9532-fa47f0b26dd4', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_b21f8ff0-fadf-4467-bc51-64b233d023c2","type":53,"name":"包容分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_1971a7b7-8648-4183-8862-6c7e1962fac6","type":50,"name":"包容条件1","showText":"(输入框 等于 123 ) ","conditionType":2,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"123"}]}]}},{"id":"Flow_b4a9ecfc-d873-417f-b663-48f5df6d543e","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","defaultFlow":true}]},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-21 20:17:04', '1', '2024-10-21 20:17:04', false, 1),
        (337, '7cd71bf2-8fa6-11ef-9532-fa47f0b26dd4', '52b23bc8-8fa6-11ef-9532-fa47f0b26dd4', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_b21f8ff0-fadf-4467-bc51-64b233d023c2","type":53,"name":"包容分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_1971a7b7-8648-4183-8862-6c7e1962fac6","type":50,"name":"包容条件1","showText":"(输入框 等于 123 ) ","childNode":{"id":"Activity_075f4900-fe8b-40d3-9354-c8bcaf946d71","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"123"}]}]}},{"id":"Flow_b4a9ecfc-d873-417f-b663-48f5df6d543e","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_4a49b732-bf9e-4113-a375-50ed5c7a493e","type":11,"name":"审批人2","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-21 20:17:49', '1', '2024-10-21 20:17:49', false, 1),
        (338, '82232866-8fa6-11ef-9532-fa47f0b26dd4', '52b23bc8-8fa6-11ef-9532-fa47f0b26dd4', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_b21f8ff0-fadf-4467-bc51-64b233d023c2","type":53,"name":"包容分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_1971a7b7-8648-4183-8862-6c7e1962fac6","type":50,"name":"包容条件1","showText":"(输入框 等于 123 ) ","childNode":{"id":"Activity_075f4900-fe8b-40d3-9354-c8bcaf946d71","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"123"}]}]}},{"id":"Flow_b4a9ecfc-d873-417f-b663-48f5df6d543e","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_4a49b732-bf9e-4113-a375-50ed5c7a493e","type":11,"name":"审批人2","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-21 20:17:58', '1', '2024-10-21 20:17:58', false, 1),
        (339, 'd94ff656-8fba-11ef-9f5e-ca5778fb00b9', '52b23bc8-8fa6-11ef-9532-fa47f0b26dd4', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_ec43ca7a-ec75-4bc7-940a-9ddcf563e337","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"GateWay_b21f8ff0-fadf-4467-bc51-64b233d023c2","type":53,"name":"包容分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_1971a7b7-8648-4183-8862-6c7e1962fac6","type":50,"name":"包容条件1","showText":"(输入框 等于 123 ) ","childNode":{"id":"Activity_075f4900-fe8b-40d3-9354-c8bcaf946d71","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"123"}]}]}},{"id":"Flow_b4a9ecfc-d873-417f-b663-48f5df6d543e","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_4a49b732-bf9e-4113-a375-50ed5c7a493e","type":11,"name":"审批人2","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-21 22:43:34', '1', '2024-10-21 22:43:34', false, 1),
        (340, 'test-dingding-bingxing:1:280345f0-9132-11ef-ae97-eaf49df1f932', '1629e04a-9132-11ef-ae97-eaf49df1f932', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '312', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","childNode":{"id":"GateWay_b5aaf8df-6500-48f4-a847-bf72bcc9cd18","type":52,"name":"并行分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_48ec853d-3a3b-4da3-9af6-2ddaedca3a50","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_94eb01f0-d03f-42b3-a625-78dd9706fd4d","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_b8a6887b-d79b-4eeb-a6a4-160af0626dbe","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_e8464a11-c32b-4c2f-a533-08cc0e13daf3","type":11,"name":"审批人","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]}}', 0, true, '', '1', '1', '2024-10-23 19:30:08', '1', '2024-10-23 19:30:08', false, 1),
        (341, 'test-dingding-bingxing:2:58b1e2e3-9132-11ef-ae97-eaf49df1f932', '1629e04a-9132-11ef-ae97-eaf49df1f932', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '312', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","childNode":{"id":"GateWay_b5aaf8df-6500-48f4-a847-bf72bcc9cd18","type":52,"name":"并行分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_48ec853d-3a3b-4da3-9af6-2ddaedca3a50","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_94eb01f0-d03f-42b3-a625-78dd9706fd4d","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_b8a6887b-d79b-4eeb-a6a4-160af0626dbe","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_e8464a11-c32b-4c2f-a533-08cc0e13daf3","type":11,"name":"审批人","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]}}', 0, true, '', '1', '1', '2024-10-23 19:31:29', '1', '2024-10-23 19:31:29', false, 1),
        (342, 'test-0101:1:f28242b5-9132-11ef-ae97-eaf49df1f932', 'b6772aaf-9132-11ef-ae97-eaf49df1f932', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","childNode":{"id":"Activity_007a62a5-8f3d-494a-b17a-1c7926ce7a2a","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"GateWay_1ead3b7a-06f1-455c-9755-91c3d7b987f9","type":51,"name":"条件分支","childNode":{"id":"GateWay_921c0715-d308-40f8-bd83-355b706d42bc","type":52,"name":"并行分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_47f2b502-f15e-43fa-bff4-7e527f505d49","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_21854fa9-4de2-46da-aa1d-8648b3109ca6","type":11,"name":"审批人（并行1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_76b3ebe0-6b12-4c1e-9d27-acef0d1f7502","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_4bcd8a9a-1652-4a47-96fb-174c54ee2339","type":11,"name":"审批人（并行2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]},"conditionNodes":[{"id":"Flow_a619aa87-d388-4cb0-9cce-4933c4353f62","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_580b4cbc-843c-4b76-9ac3-fbcb5954fcf2","type":11,"name":"审批人（分支条件 1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"1"}]}]}},{"id":"Flow_2669ec3a-f38d-46bd-bd49-4bc4d407003f","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_7e062a59-d3fc-430a-a1cc-8c1ca042fb60","type":11,"name":"审批人（默认条件支付）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}', 0, true, '', '1', '1', '2024-10-23 19:35:47', '1', '2024-10-23 19:35:47', false, 1),
        (343, 'test-0101:2:44c67133-9133-11ef-ae97-eaf49df1f932', 'b6772aaf-9132-11ef-ae97-eaf49df1f932', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"GateWay_1ead3b7a-06f1-455c-9755-91c3d7b987f9","type":51,"name":"条件分支","childNode":{"id":"GateWay_921c0715-d308-40f8-bd83-355b706d42bc","type":52,"name":"并行分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_47f2b502-f15e-43fa-bff4-7e527f505d49","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_21854fa9-4de2-46da-aa1d-8648b3109ca6","type":11,"name":"审批人（并行1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_76b3ebe0-6b12-4c1e-9d27-acef0d1f7502","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_4bcd8a9a-1652-4a47-96fb-174c54ee2339","type":11,"name":"审批人（并行2）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]},"conditionNodes":[{"id":"Flow_a619aa87-d388-4cb0-9cce-4933c4353f62","type":50,"name":"条件1","showText":"(输入框 等于 1 ) ","childNode":{"id":"Activity_580b4cbc-843c-4b76-9ac3-fbcb5954fcf2","type":11,"name":"审批人（分支条件 1）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"conditionType":2,"defaultFlow":false,"conditionGroups":{"and":true,"conditions":[{"and":true,"rules":[{"opCode":"==","leftSide":"F1j4m2guo62qbdc","rightSide":"1"}]}]}},{"id":"Flow_2669ec3a-f38d-46bd-bd49-4bc4d407003f","type":50,"name":"其它情况","showText":"未满足其它条件时，将进入此分支","childNode":{"id":"Activity_7e062a59-d3fc-430a-a1cc-8c1ca042fb60","type":11,"name":"审批人（默认条件支付）","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"defaultFlow":true}]},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-23 19:38:06', '1', '2024-10-23 19:38:06', false, 1),
        (344, 'test-dingding-bingxing:3:9738183e-9133-11ef-ae97-eaf49df1f932', '1629e04a-9132-11ef-ae97-eaf49df1f932', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '312', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","childNode":{"id":"Activity_b6b02fb9-3310-4bae-8be1-ae4e92c898dc","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"GateWay_b5aaf8df-6500-48f4-a847-bf72bcc9cd18","type":52,"name":"并行分支","childNode":{"id":"EndEvent","type":1,"name":"结束"},"conditionNodes":[{"id":"Flow_48ec853d-3a3b-4da3-9af6-2ddaedca3a50","type":50,"name":"并行1","showText":"无需配置条件同时执行","childNode":{"id":"Activity_94eb01f0-d03f-42b3-a625-78dd9706fd4d","type":11,"name":"审批人1","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}},{"id":"Flow_b8a6887b-d79b-4eeb-a6a4-160af0626dbe","type":50,"name":"并行2","showText":"无需配置条件同时执行","childNode":{"id":"Activity_e8464a11-c32b-4c2f-a533-08cc0e13daf3","type":11,"name":"审批人","showText":"指定成员：芋道源码","candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}]},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}}}', 0, true, '', '1', '1', '2024-10-23 19:40:24', '1', '2024-10-23 19:40:24', false, 1),
        (345, 'test03:1:d5625d34-9373-11ef-a841-e2a58ed7a101', 'a851318f-9373-11ef-a841-e2a58ed7a101', 10, 'http://test.yudao.iocoder.cn/aa10f3fe6a6643ddf3284ce3f0e5573904d4e1d1844a37ea342f7069db1f32e2.png', '', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', null, 0, true, '', '1', '1', '2024-10-26 16:25:18', '1', '2024-10-26 16:25:18', false, 1),
        (346, 'test-normal:1:3c5d5053-9380-11ef-b1a3-12361b4cbb3f', '1844be5e-9380-11ef-b1a3-12361b4cbb3f', 10, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', null, 0, true, '', '1', '1', '2024-10-26 17:54:05', '1', '2024-10-26 17:54:05', false, 1),
        (347, 'test-auto:1:c70a799a-9394-11ef-a039-7a9ac3d9eb6b', 'bc6ba9f4-9394-11ef-a039-7a9ac3d9eb6b', 20, 'http://test.yudao.iocoder.cn/7f57be8f3808693f9a5fa78277c6f4ec55cc69f6b61760683498d7112f0dbed2.jpg', 'abaab', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_aa24c0cb-a95c-41f4-9949-faefa0ea076d","type":11,"name":"审批人","showText":"自动通过","childNode":{"id":"EndEvent","type":1,"name":"结束"},"approveType":2,"approveMethod":4,"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1730548270469, true, '', '1', '1', '2024-10-26 20:21:08', '1', '2024-11-02 19:51:10', false, 1),
        (348, 'test-auto:2:f4681edb-9430-11ef-919e-2224f84471e0', 'bc6ba9f4-9394-11ef-a039-7a9ac3d9eb6b', 20, 'http://test.yudao.iocoder.cn/7f57be8f3808693f9a5fa78277c6f4ec55cc69f6b61760683498d7112f0dbed2.jpg', 'abaab', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_aa24c0cb-a95c-41f4-9949-faefa0ea076d","type":11,"name":"审批人","showText":"自动通过","childNode":{"id":"EndEvent","type":1,"name":"结束"},"approveType":2,"approveMethod":4,"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1730548270469, true, '1', '1', '1', '2024-10-27 14:59:05', '1', '2024-11-02 19:51:10', false, 1),
        (349, 'test-role:1:317330f3-9511-11ef-8377-6a0da1cb9473', '27c020dd-9511-11ef-8377-6a0da1cb9473', 20, 'http://test.yudao.iocoder.cn/06f9d34abad55094348b4cbb3bb2d5a3da4d1c515c6b12e3d6fe64a940593f33.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_733ed9e6-c572-4e22-ae14-09b1fb3a810e","type":11,"name":"审批人","showText":"指定角色：测试账号","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":10,"candidateParam":"101","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-28 17:44:15', '1', '2024-10-28 17:44:15', false, 1),
        (350, 'test-role:2:43135e47-9511-11ef-8377-6a0da1cb9473', '27c020dd-9511-11ef-8377-6a0da1cb9473', 20, 'http://test.yudao.iocoder.cn/06f9d34abad55094348b4cbb3bb2d5a3da4d1c515c6b12e3d6fe64a940593f33.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_733ed9e6-c572-4e22-ae14-09b1fb3a810e","type":11,"name":"审批人","showText":"指定角色：测试账号,超级管理员","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":10,"candidateParam":"101,1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-28 17:44:44', '1', '2024-10-28 17:44:44', false, 1),
        (351, 'test-all-assign:1:76676b40-95f5-11ef-950f-3a242f068f0f', 'e685770a-95f4-11ef-950f-3a242f068f0f', 20, 'http://test.yudao.iocoder.cn/83852d802b3aab78ba9badf700079178d352d6a946f7c748ad8e1d3172f82032.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_736f1407-9122-4260-9144-112cbaa39f37","type":11,"name":"审批人（指定成员）","showText":"指定成员：芋道源码,芋道","childNode":{"id":"Activity_08f0c04e-e3ba-44c9-b88f-d3b050327266","type":11,"name":"审批人（指定角色）","showText":"指定角色：测试账号,超级管理员","childNode":{"id":"Activity_0395ff02-43ad-4c74-a62e-8f11a6f60a6d","type":11,"name":"审批人（部门成员）","showText":"部门成员：芋道源码","childNode":{"id":"Activity_fb6572e9-fad7-4747-9b2b-c905df51e2a4","type":11,"name":"审批人（部门负责人）","showText":"部门的负责人：深圳总公司","childNode":{"id":"Activity_56e0e04f-dc67-49c9-8aab-3b74d7d2038c","type":11,"name":"审批人（连续多级部门负责人）","showText":"多级部门的负责人：研发部门","childNode":{"id":"Activity_02629fab-4fea-4c30-8d85-02b48e382600","type":11,"name":"审批人（发起人自选）","showText":"发起人自选","childNode":{"id":"Activity_76411cec-6229-4770-886c-f7eace8638a8","type":11,"name":"审批人（发起人本人）","showText":"发起人自己","childNode":{"id":"Activity_c40c3877-09c1-4ca6-a4a3-9de3da767163","type":11,"name":"审批人（发起人部门负责人）","showText":"发起人的部门负责人","childNode":{"id":"Activity_d704a6c2-d07d-42ee-ba49-018c74b7ecc2","type":11,"name":"审批人（发起人连续部门负责人）","showText":"发起人连续部门负责人","childNode":{"id":"Activity_929b02ef-37ea-40b5-b750-b2c409af32dd","type":11,"name":"审批人（用户组）","showText":"指定用户组: 测试一下","childNode":{"id":"Activity_e3d77e25-4c4e-48ed-ae6c-f83b07ae1b5c","type":11,"name":"审批人（流程表达式）","showText":"流程表达式：${1}","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":60,"candidateParam":"${1}","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":40,"candidateParam":"113","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":38,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":37,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":36,"approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":35,"approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":23,"candidateParam":"103|1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":21,"candidateParam":"101","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":20,"candidateParam":"100","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":10,"candidateParam":"101,1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1,100","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 0, true, '', '1', '1', '2024-10-29 20:58:16', '1', '2024-10-29 20:58:16', false, 1),
        (352, 'test-liuchengbiaodan:1:4ced4eb4-98ce-11ef-8ece-de51f13f22dd', '3b93041f-98ce-11ef-8ece-de51f13f22dd', 10, 'http://test.yudao.iocoder.cn/6d68537ef4286cfd9a5fc0a550f48dfc8ed15c9f847036621724b07d32a571b5.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', null, 1730542262629, true, '', '1', '1', '2024-11-02 11:55:29', '1', '2024-11-02 18:11:03', false, 1),
        (353, 'test-liuchengbiaodan:2:8c64ae6a-9901-11ef-a7a1-0273c4a653d6', '3b93041f-98ce-11ef-8ece-de51f13f22dd', 10, 'http://test.yudao.iocoder.cn/6d68537ef4286cfd9a5fc0a550f48dfc8ed15c9f847036621724b07d32a571b5.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', null, 1730542262629, true, '', '1', '1', '2024-11-02 18:02:20', '1', '2024-11-02 18:11:03', false, 1),
        (354, 'test-liuchengbiaodan:3:8eda424e-9901-11ef-a7a1-0273c4a653d6', '3b93041f-98ce-11ef-8ece-de51f13f22dd', 10, 'http://test.yudao.iocoder.cn/6d68537ef4286cfd9a5fc0a550f48dfc8ed15c9f847036621724b07d32a571b5.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', null, 1730542262629, true, '', '1', '1', '2024-11-02 18:02:24', '1', '2024-11-02 18:11:03', false, 1),
        (355, 'test-auto:3:06e17e32-9902-11ef-a7a1-0273c4a653d6', 'bc6ba9f4-9394-11ef-a039-7a9ac3d9eb6b', 20, 'http://test.yudao.iocoder.cn/7f57be8f3808693f9a5fa78277c6f4ec55cc69f6b61760683498d7112f0dbed2.jpg', 'abaab', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_aa24c0cb-a95c-41f4-9949-faefa0ea076d","type":11,"name":"审批人","showText":"自动通过","childNode":{"id":"EndEvent","type":1,"name":"结束"},"approveType":2,"approveMethod":4,"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1730548270469, true, '1', '1', '1', '2024-11-02 18:05:46', '1', '2024-11-02 19:51:10', false, 1),
        (356, 'test-new-dd:1:f91a81a8-99bc-11ef-a45e-3a78b98a63cb', 'ee82ddf2-99bc-11ef-a45e-3a78b98a63cb', 20, 'http://test.yudao.iocoder.cn/e5cca593a8f24433afa4806a21b7b2284d07bc159aec55da7e7091d497a0e023.png', '', 10, 24, '{"form":{"labelPosition":"right","size":"small","labelWidth":"125px","hideRequiredAsterisk":false,"showMessage":true,"inlineMessage":false},"submitBtn":{"show":true,"innerText":"提交"},"resetBtn":{"show":false,"innerText":"重置"}}', '["{\\"type\\":\\"input\\",\\"field\\":\\"Fjph60d8vlrl9\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_drag_tag\\":\\"input\\",\\"hidden\\":false,\\"display\\":true}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_a4b03090-68ad-4729-ad8f-7af95c34dba9","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"Fjph60d8vlrl9","title":"输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1730622220621, true, '', '1', '1', '2024-11-03 16:23:58', '1', '2024-11-03 16:23:58', false, 1),
        (357, 'test-start-user-select:1:6e99b91c-9dc9-11ef-b43f-ee8532398633', '5765ef27-9dc9-11ef-b43f-ee8532398633', 10, 'http://test.yudao.iocoder.cn/000e449d651ddf162c5603cbef8aed7446d2406cf311160b4e11a46444df5e7b.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', null, 1731067355205, true, '', '1', '1', '2024-11-08 20:03:14', '1', '2024-11-08 20:03:14', false, 1),
        (358, 'test-000001:1:b2da0708-9f14-11ef-9f88-ca5778fb00b9', '9d515502-9f14-11ef-9f88-ca5778fb00b9', 20, 'http://test.yudao.iocoder.cn/000e449d651ddf162c5603cbef8aed7446d2406cf311160b4e11a46444df5e7b.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_e518bc01-7f92-44e2-b10c-bdbb5134408f","type":12,"name":"抄送人","showText":"发起人自选","childNode":{"id":"Activity_bb7d66a7-61bc-4e0b-a1dd-3dd8bec58746","type":11,"name":"审批人","showText":"发起人自选","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":35,"approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":35,"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}]},"fieldsPermission":[{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"2"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1731209635926, true, '', '1', '1', '2024-11-10 11:34:32', '1', '2024-11-10 11:34:32', false, 1),
        (359, 'test-form-001:1:f6ea9c4b-a8cc-11ef-bfaf-6ef3f2246243', 'e902bc25-a8cc-11ef-bfaf-6ef3f2246243', 20, 'http://test.yudao.iocoder.cn/b46434bd8912c64a662cb3043e6f87e59ecf9a51c9dbcbc66d3699742b8f6f07.png', '', 10, 31, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"100px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"新表单"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"F1j4m2guo62qbdc\\",\\"title\\":\\"输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"_fc_id\\":\\"id_F9jam2guo62qbec\\",\\"name\\":\\"ref_Fzq1m2guo62qbfc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"input\\"}","{\\"type\\":\\"input\\",\\"field\\":\\"Fdmkm2guoa4mbgc\\",\\"title\\":\\"多行输入框\\",\\"info\\":\\"\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"textarea\\"},\\"_fc_id\\":\\"id_Ftlim2guoa4mbhc\\",\\"name\\":\\"ref_Fl0cm2guoa4mbic\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"textarea\\"}"]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_b059eb3b-70a9-4976-b71f-ce4e1601a219","type":11,"name":"审批人","showText":"指定成员：芋道源码","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[{"field":"PROCESS_START_USER_ID","title":"发起人","permission":"1"},{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"1"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"1"}],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":true},{"id":4,"displayName":"委派","enable":true},{"id":5,"displayName":"加签","enable":true},{"id":6,"displayName":"退回","enable":true}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[{"field":"PROCESS_START_USER_ID","title":"发起人","permission":"1"},{"field":"F1j4m2guo62qbdc","title":"输入框","permission":"3"},{"field":"Fdmkm2guoa4mbgc","title":"多行输入框","permission":"2"}],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"退回","enable":false}]}', 1732278350781, true, '', '1', '1', '2024-11-22 20:26:14', '1', '2024-11-22 20:26:14', false, 1),
        (1876921125693460481, 'apr_product_firstsale:1:5df23e04-cda1-11ef-8966-acde48001122', 'e5fc402f-cda0-11ef-8966-acde48001122', 10, 'http://files.test.ybm100.com/INVT/Lzinq/20250108/53073a08018d60d68d92ca9417f454a3ef79e3065399e01e54f4c91aef875ae8.jpg', '商品首营审批（三级审批）', 10, 1876915391819845633, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"密码确认"}', '["{\\"type\\":\\"input\\",\\"field\\":\\"aprPassword\\",\\"title\\":\\"密码\\",\\"info\\":\\"请输入密码\\",\\"$required\\":false,\\"props\\":{\\"type\\":\\"password\\"},\\"_fc_id\\":\\"id_Fss5m5nnv89zaic\\",\\"name\\":\\"ref_Fpgdm5nnv89zajc\\",\\"display\\":true,\\"hidden\\":false,\\"_fc_drag_tag\\":\\"password\\"}"]', '', '', null, 0, true, '', '1', '1', '2025-01-08 17:17:23', '1', '2025-01-08 17:17:23', false, 1),
        (1876922132791988226, 'apr_product_firstsale:2:ece00e38-cda1-11ef-8966-acde48001122', 'e5fc402f-cda0-11ef-8966-acde48001122', 10, 'http://files.test.ybm100.com/INVT/Lzinq/20250108/53073a08018d60d68d92ca9417f454a3ef79e3065399e01e54f4c91aef875ae8.jpg', '商品首营审批（三级审批）', 20, null, null, null, '/product/firstsale/create', '/product/firstsale/list', null, 0, true, '', '1', '1', '2025-01-08 17:21:23', '1', '2025-01-08 17:21:23', false, 1),
        (1877185387410165762, 'oa_leave:1:0fce3ccc-ce34-11ef-a485-acde48001122', 'a74d00a7-ce33-11ef-a485-acde48001122', 10, 'http://files.test.ybm100.com/INVT/Lzinq/20250109/468eebc4264164928cdde5ae7d0e1265e90441bbb00d79d4863b34db2a8ac4ea.jpg', '请加', 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave', null, 0, true, '', '1', '1', '2025-01-09 10:47:27', '1', '2025-01-09 10:47:27', false, 1),
        (1877231012340244481, 'oa_leave:2:633c6286-ce4d-11ef-96af-acde48001122', 'a74d00a7-ce33-11ef-a485-acde48001122', 20, 'http://files.test.ybm100.com/INVT/Lzinq/20250109/468eebc4264164928cdde5ae7d0e1265e90441bbb00d79d4863b34db2a8ac4ea.jpg', '请加', 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_a8411763-9284-4c6c-bcf6-c8088a3bb736","type":11,"name":"一级审批","showText":"指定角色：超级管理员","childNode":{"id":"Activity_0b00cce0-ba7c-4204-a43a-22cf2551e4c5","type":11,"name":"审批人","showText":"指定角色：超级管理员","childNode":{"id":"Activity_2f4442a0-e689-4366-8119-b797b92e24d4","type":11,"name":"审批人","showText":"指定角色：超级管理员","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":10,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":10,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":10,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2025-01-09 13:48:45', '1', '2025-01-09 13:48:45', false, 1),
        (1877234535601741826, 'oa_leave:3:57b39680-ce4f-11ef-96af-acde48001122', 'a74d00a7-ce33-11ef-a485-acde48001122', 20, 'http://files.test.ybm100.com/INVT/Lzinq/20250109/468eebc4264164928cdde5ae7d0e1265e90441bbb00d79d4863b34db2a8ac4ea.jpg', '请加', 20, null, null, null, '/bpm/oa/leave/create', '/bpm/oa/leave', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_a8411763-9284-4c6c-bcf6-c8088a3bb736","type":11,"name":"一级审批","showText":"指定角色：超级管理员","childNode":{"id":"Activity_0b00cce0-ba7c-4204-a43a-22cf2551e4c5","type":11,"name":"二级审批","showText":"自动通过","childNode":{"id":"Activity_2f4442a0-e689-4366-8119-b797b92e24d4","type":11,"name":"三级审批","showText":"指定角色：超级管理员","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":10,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":10,"candidateParam":"1","approveType":2,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":10,"candidateParam":"1","approveType":1,"approveMethod":4,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":true,"type":1,"timeDuration":"PT6H","maxRemindCount":1},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2025-01-09 14:02:45', '1', '2025-01-09 14:02:45', false, 1),
        (1881535982396915713, 'pd_product_first_apr:1:20538424-d7a3-11ef-9dd9-acde48001122', 'f1a42e4e-d7a1-11ef-9dd9-acde48001122', 20, 'http://files.test.ybm100.com/INVT/Lzinq/20250121/f3e8ff125d5b731e7a93a894dce3edf8f684f3bdc7983f2025d44cf60d0ce5b5.jpg', '', 10, 1881534030376222722, '{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"}}', '[]', '', '', '{"id":"StartUserNode","type":10,"name":"发起人","showText":"已设置","childNode":{"id":"Activity_6f7c0528-66ae-4a1c-93d3-f1b47ae2b34c","type":11,"name":"一级审批","showText":"指定成员：超管","childNode":{"id":"Activity_ae868407-1a43-4612-8945-368610154136","type":11,"name":"二级审批","showText":"指定成员：超管","childNode":{"id":"Activity_0b8f3eb9-0757-499e-8c15-13dc6202cc13","type":11,"name":"三级审批","showText":"指定成员：超管","childNode":{"id":"EndEvent","type":1,"name":"结束"},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"candidateStrategy":30,"candidateParam":"1","approveType":1,"approveMethod":1,"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"通过","enable":true},{"id":2,"displayName":"拒绝","enable":true},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}],"rejectHandler":{"type":1,"returnNodeId":null},"timeoutHandler":{"enable":false,"type":null,"timeDuration":null,"maxRemindCount":null},"assignStartUserHandlerType":1,"assignEmptyHandler":{"type":1,"userIds":null}},"fieldsPermission":[],"buttonsSetting":[{"id":1,"displayName":"提交","enable":true},{"id":2,"displayName":"拒绝","enable":false},{"id":3,"displayName":"转办","enable":false},{"id":4,"displayName":"委派","enable":false},{"id":5,"displayName":"加签","enable":false},{"id":6,"displayName":"回退","enable":false}]}', 0, true, '', '1', '1', '2025-01-21 10:55:10', '1', '2025-01-21 10:55:10', false, 1);