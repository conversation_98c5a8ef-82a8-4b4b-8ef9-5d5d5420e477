insert into xyy_saas_system.saas_transmission_config_item (id, config_package_id, parent_item_id, dsl_type, node_type, api_code, description, config_value, disable, creator, create_time, updater, update_time, deleted)
values  (1, 1, 0, 2, 30001, '', '互联网监管-上传门诊病例', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')
postParameter:
  nodes:
    - clinicalCase
    - doctorInfo
    - pharmacistInfo
#    - medicalRegistrationInfo
dependency:
  downstreamNodes:
    - 30002', false, '1', '2025-02-20 15:01:18', '1', '2025-04-07 14:03:04', false),
        (2, 1, 0, 2, 30002, '', '互联网监管-线上处方点评', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')
postParameter:
  nodes:
    - doctorInfo
    - pharmacistInfo
    #    - medicalRegistrationInfo
    - prescriptionDetail
    - clinicalCase
    - prescriptionCa
    - inquiryDetailInfo
    - stdlibProduct
dependency:
  downstreamNodes:
    - 30003', false, '1', '2025-02-20 15:01:18', '1', '2025-04-07 14:01:54', false),
        (3, 1, 0, 2, 30003, '', '互联网监管-诊疗结算', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')
postParameter:
  nodes:
    - doctorInfo
    - pharmacistInfo
    - medicalRegistrationInfo
    - prescriptionDetail
    - clinicalCase
    - operateUserInfo
    - inquiryDetailInfo
dependency:
  downstreamNodes:
    - 30004', false, '1', '2025-02-20 15:01:18', '1', '2025-04-07 14:02:12', false),
        (4, 1, 0, 2, 30004, '', '互联网监管-派药', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')', false, '1', '2025-02-20 15:01:18', '1', '2025-04-02 15:45:12', false),
        (8, 2, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-02-20 15:01:18', '1', '2025-03-20 11:52:10', false),
        (9, 2, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-02-20 15:01:18', '1', '2025-03-20 11:52:10', false),
        (10, 2, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-02-20 15:01:18', '1', '2025-02-26 19:37:22', false),
        (11, 1, 0, 3, 30001, 'Q341', '互联网监管-上传门诊病例', 'dslType: contract
enable: true
name: 门诊病例
format: json
functions:
  - path: "''/rainbow/api/hpcp/hospolmonitor/outpatientTreat''"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "''Q341''"
          "body":
            - "yljgdm": "''H50010701844''" # 医疗机构代码
              "userName": "''重庆格林医院''" # 医疗机构名称
              "credentialType": "''01''" # 证件类型 01身份证
              "credentialNum": business.data[''data''][''idCard''] # 证件号码
              "medicalNum": business.data[''data''][''inquiryPref''] # 就诊流水号
              "outpatientNumber": business.data[''aux''][''clinicalCase'']?.iptOtpNo # 门诊号 仅实体医疗机构必传
              "doctorCode": business.data[''aux''][''clinicalCase'']?.doctorHospitalPref # 接诊医生编号
              "doctorName": business.data[''aux''][''clinicalCase'']?.doctorName # 接诊医生姓名
              "fzbz": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.followUp,''0'') ? ''0'' : ''1''" # 复诊标志
              "inquirydate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''aux''][''clinicalCase'']?.startTime) # 问诊时间
              "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''dept_dict'',business.data[''aux''][''clinicalCase'']?.deptPref)" # 科室编码，非空字典映射，系统的科室编码
              "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDictLabel(business,''dept_dict'',business.data[''aux''][''clinicalCase'']?.deptPref,business.data[''aux''][''clinicalCase'']?.deptName)" # 科室名称，非空系统的科室名称
              "zsjl": T(org.apache.commons.lang3.StringUtils).joinWith('','',business.data[''aux''][''clinicalCase'']?.mainSuit) # 主诉记录
              "zyzzmc": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.mainSymptoms,''暂无'')" # 主要症状
              "gmsbz": "T(cn.hutool.core.collection.CollUtil).isEmpty(business.data[''aux''][''clinicalCase'']?.allergic) ? ''1'' : ''2''" # 过敏史标志
              "gmsms": T(org.apache.commons.lang3.StringUtils).joinWith('','',business.data[''aux''][''clinicalCase'']?.allergic) # 过敏史描述
              "xbsms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.currentIllnessDesc,''暂无'')"  # 现病史描述
              "jwsms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.patientHisDesc,''暂无'')" # 既往史描述
              "zyszms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.ext?.tcmFourDiagnosticDesc,''暂无'')" # 中医“四诊”等描述
              "zxyzdbz": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.medicineType,0) ? ''2'' : ''1'' " # 中医/西医病历标志
              "sfscsx": "business.data[''aux''][''clinicalCase'']?.ext?.tcmUploadTongueImage == null ? ''0'' : business.data[''aux''][''clinicalCase'']?.ext?.tcmUploadTongueImage" # 是否上传舌象
              "bzfx": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.ext?.tcmDialecticalAnalysis,''暂无'')" # 辨证分析
              "additionalDiagnosisList": # 诊断列表
                - "diagnosisCode": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisCode''] # 诊断编码
                  "diagnosisName": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisName''] # 诊断名称
                  "diagnosisClassify": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisClassify''],0) ? ''2'' : ''1''"  # 诊断分类
                  "diagnosisType": "''7''" # 诊断类型 默认其他
                  "diagSort": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''index''] # 诊断排序
              "zxdm": business.data[''aux''][''clinicalCase'']?.tcmSyndromeCode # 中医辨证代码
              "zxmc": business.data[''aux''][''clinicalCase'']?.tcmSyndromeName # 中医辨证名称
              "therapyList": # 中医治法列表
                - "zfdm": business.data[''aux''][''clinicalCase'']?.tcmTreatmentMethodCode # 治法代码
                  "zfmc": business.data[''aux''][''clinicalCase'']?.tcmTreatmentMethodName # 治法名称
              "jzzdsm": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.ext?.outpatientDiagnosisDesc,''暂无'')" # 门诊诊断说明
              "clcs": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''clinicalCase'']?.measures,''暂无'')" # 处理措施
              "sfxylygc": "business.data[''aux''][''clinicalCase'']?.observation == null ? ''0'' : business.data[''aux''][''clinicalCase'']?.observation" # 是否需要留院（入/转院）观察
              "referral": "business.data[''aux''][''clinicalCase'']?.referral == null ? ''0'' : business.data[''aux''][''clinicalCase'']?.referral " # 转诊
              "jdrq": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 建档日期
              "tbrq": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 填报日期
    response:
      "infcode": "[''package''][''additionInfo''][errorCode]"
      "err_msg": "[''package''][''additionInfo''][errorMsg]"
', false, '1', '2025-02-20 15:01:18', '1', '2025-04-11 16:41:20', false),
        (12, 1, 0, 3, 9999901, '', '互联网监管-公共节点', 'domain: business.data[''network''][''networkItem''][''common'']
#path:
protocol: http
header:
  "x-rb-key": business.data[''network'']?.thirdPartyPublicKey
  "x-rb-timestamp": T(java.lang.System).currentTimeMillis()
  "x-rb-requuid": T(cn.hutool.core.util.IdUtil).fastSimpleUUID()
  "x-rb-sign": T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestSignUtil).sign4CqSupervision(business.data[''network'']?.thirdPartyPublicKey,business.data[''network'']?.thirdPartyPrivateKey,commonHeader[''x-rb-timestamp''],commonHeader[''x-rb-requuid''])
  "Content-Type": "''application/json''"
input:
  "package":
    "head":
      #时间(14)+顺序号(4) 时间格式：yyyyMMddHHmmss
      "sendTradeNum": T(cn.hutool.core.date.DateUtil).date().toString("yyyyMMddHHmmss")+T(java.lang.String).format("%04d", T(cn.hutool.core.util.IdUtil).createSnowflake(1, 1).nextId() % 10000) #医院消息唯一码
      "senderCode": "''400080041''" #医院编码400XXX
      "senderName": "''重庆格林医院互联网医院''" #医院名称
      "CACode": "''CA014''" #医院采用的电子签名厂商编码
      "CAName": "''东方中讯数字证书认证有限公司''" #医院采用的电子签名厂商名称
      "receiverCode": "''YY0000''" #互联网医疗服务监管平台唯一码
      "receiverName": "''互联网医疗服务监管平台''" #互联网医疗服务监管平台
      "intermediaryCode": "''hywz''"
      "intermediaryName": "''荷叶问诊''"
      "hosorgNum": "''001''"
      "hosorgName": "''小荷''"
      "systemType": "''1''"
      "busenissType": "''8''"
      "standardVersionCode": "''version:1.0.0''"
      "clientmacAddress": "''7C8AE1CB0010''"
      "recordCount": "''1''"
    "additionInfo":
      "curDllAddr": "''''"
      "receiverTradeNum": "''''"
      "asyncAsk": "''0''"
      "errorCode": "''''"
      "callback": "''''"
      "correlationId": "''9909''"
      "errorMsg": "''''"
#    "body":

result:
#  success: true
  success: output[infcode] == ''0''
  tips: output[err_msg]', false, '1', '2025-02-20 15:01:18', '1', '2025-04-11 14:52:13', false),
        (13, 2, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-02-20 15:01:18', '1', '2025-02-26 17:39:21', false),
        (14, 2, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', true, '1', '2025-02-20 15:01:18', '1', '2025-04-08 17:06:59', false),
        (15, 2, 0, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-02-20 15:01:18', '1', '2025-02-21 13:09:40', false),
        (16, 1, 0, 3, 30002, 'Q310', '互联网监管-线上处方点评', 'dslType: contract
enable: true
name: 线上处方点评
format: json
functions:
  - path: "''/rainbow/api/hpcp/hospolmonitor/prescriptionReview''"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "''Q310''"
          "body":
            - "patientNo": business.data[''data''][''patientPref''] # 患者编号，医院患者编号
              "medicalNum": business.data[''data''][''inquiryPref''] # 就诊流水号，医院门诊号，医疗机构内部门诊就诊唯一编号
              "patientName": business.data[''data''][''fullName''] # 患者姓名
              "patientSex": business.data[''data''][''patientSex''] # 患者性别，字典映射：1：男性；2：女性；9：未说明性别
              "patientIdType": "''01''" # 证件类型，字典映射
              "patientIdNo": business.data[''aux''][''inquiryDetailInfo'']?.patientIdCard # 证件号码
              "birthday": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data[''aux''][''inquiryDetailInfo'']?.patientIdCard)"  # 出生日期，格式YYYYMMDD
              "race": "''01''" # 民族，字典映射 - 默认汉族
              "recipeNum": business.data[''data''][''pref''] # 处方号
              "recipeStatus": "''0''" # 处方状态，字典映射：0.正常处方；1.退药或者其他作废处方，不传默认0
              "recipeType": "''1''" # 处方类别，字典映射：1.普通处方；2.儿科处方；3.麻醉处方；4.急诊处方；5.其他处方
              "recipeSource": "''3''" # 处方来源：1.门诊；2.急诊； 3.其他
              "epitaxy": "''1''" # 1非外延，2外延
              "recipeCategory": "T(java.util.Objects).equals(business.data[''data''][''medicineType''],0) ? ''3'' : ''1'' " # 处方类型，字典映射：1.草药方；2.中成药方；3.西药方
              "deptCode": business.data[''data''][''deptPref''] # 科室编码，字典映射
              "deptName": business.data[''data''][''deptName''] # 科室名称
              "hosDeptCode": business.data[''data''][''deptPref''] # 医院科室编码，院内科室编码
              "hosDeptName": business.data[''data''][''deptName''] # 医院科室名称，院内科室名称
              "recipeDocCode": business.data[''aux'']?.get(''doctorInfo'')?.doctorHospitalPref # 开方医生编码
              "recipeDocName": business.data[''data''][''doctorName''] # 开方医生姓名
              "recipeDocTitle": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''doctor_title'',business.data[''aux''][''doctorInfo'']?.titleCode)" # 开方医生职称，字典映射
              "recipeDocSignatureValue": business.data[''aux'']?.get(''prescriptionCa'')?.get(''doctorSign'')?.signValue  # 开方医生数字签名值，需是符合PKCS#1格式规范的电子签名值，仅互联网诊疗必传
              "recipeDocSigncertificate": business.data[''aux''][''prescriptionCa'']?.get(''doctorSign'')?.cerFile  # 开方医生数字证书，需是符合X509格式规范的BASE64编码数字证书值，需为SM2证书，仅互联网诊疗必传
              "recipeDocTimeStamp": business.data[''aux''][''prescriptionCa'']?.get(''doctorSign'')?.signTimestamp  # 开方医生时间戳，需是符合SM2国密标准的BASE64编码时间戳值，仅互联网诊疗必传
              "recipeDocSignatureXML": "T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestParamUtil).getCqCaXmlParam(business.data[''data''],business.data[''aux''],true)" # 开方医生数字签名对象数据结构XML
              "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''outPrescriptionTime'']) # 开方时间，格式YYYYMMDDHH24MISS
              "trialPharmCode": business.data[''data''][''pharmacistPref''] # 审方药师编码，仅互联网诊疗必传
              "trialPharmName": business.data[''data''][''pharmacistName''] # 审方药师名称，仅互联网诊疗必传
              "trialPharmTitle": "''244''" # 审方药师职称，字典映射，仅互联网诊疗必传  244-药师
              "trialPharmSignatureValue": business.data[''aux''][''prescriptionCa'']?.get(''pharmacistSign'')?.signValue # 审方药师数字签名值，需是符合PKCS#1格式规范的电子签名值，仅互联网诊疗必传
              "trialPharmSigncertificate": business.data[''aux''][''prescriptionCa'']?.get(''pharmacistSign'')?.cerFile # 审方药师数字证书，需是符合X509格式规范的BASE64编码数字证书值，需为SM2证书，仅互联网诊疗必传
              "trialPharmTimeStamp": business.data[''aux''][''prescriptionCa'']?.get(''pharmacistSign'')?.signTimestamp # 审方药师时间戳，需是符合SM2国密标准的BASE64编码时间戳值，仅互联网诊疗必传
              "trialPharmSignatureXML": "T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestParamUtil).getCqCaXmlParam(business.data[''data''],business.data[''aux''],false)"  # 审方药师数字签名对象数据结构XML
              "trialDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''auditPrescriptionTime'']) # 审方时间，格式YYYYMMDDhh24:mi:ss
              "recipeFeeTotal": business.data[''data''][''ext''][''pricingPrice''] # 处方金额，2位小数
              "recipedistribut": "''3''" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送，不传默认为1，仅互联网诊疗必传
              "keepUseFlag": "''1''" # 继用处方标识，1非继用，2继用，不传默认1
              "longReciptFlag": "''1''" # 是否长处方标识，1非长处方2长处方，不传默认1
              "additionalDiagnosisList": # 诊断列表
                - "diagnosisCode": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisCode''] # 诊断编码，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisName": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisName''] # 诊断名称，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisClassify": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisClassify''],0) ? ''2'' : ''1''"  # 诊断分类，字典映射：1中医诊断，2西医诊断，中医病历时必有中医诊断和西医诊断
                  "diagnosisType": "''7''" # 诊断类型，字典映射，说明：医院诊断和对应的医保诊断都需要上传，如果非医保患者默认本地职工对应的诊断
                  "diagSort": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''index''] # 诊断排序
              "symptomCode": business.data[''aux''][''clinicalCase'']?.tcmSyndromeCode # 中医辨证编码，中医必填
              "symptomCodeName": business.data[''aux''][''clinicalCase'']?.tcmSyndromeName # 中医辨证名称，中医必填
              "drugList": # 处方药品明细信息
                - "recipeSerialNum": _index_ # 处方流水号，Number类型，同一个就诊下，处方流水号在中心端能够唯一标识一条处方明细信息
                  "groupNo": "''1''" # 配伍组号，药物分组使用时的组号
                  "hospitalDrugCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 药品编号，医院药品编号
                  "drugCommonName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 药品通用名
                  "drugBrandName": business.data[''aux''][''prescriptionDetail''][_index_][''productName''] # 药品商品名，如泰诺等
                  "drugDose": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''singleDose''],business.data[''data''][''ext''][''tcmDailyDosage''])" # 单次给药剂量，包含饮片单次剂量
                  "drugDoseUnit": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_dose_unit'',  T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data[''aux''][''prescriptionDetail''][_index_][''singleUnit''],''g'') )" # 单次给药剂量单位，字典映射
                  "medicationRoute": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_directions'',T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data[''aux''][''prescriptionDetail''][_index_][''directions''],business.data[''data''][''ext''][''tcmDirections''] ) ) " # 给药途径，字典映射：包括饮片给药途径
                  "frequency": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_use_frequency'',T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data[''aux''][''prescriptionDetail''][_index_][''useFrequency''],''2次/天'' ) )" # 给药频率，字典映射
                  "formulation": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''dosageForm''],''-'')" # 剂型，包含饮片剂型
                  "spec": business.data[''aux''][''prescriptionDetail''][_index_][''attributeSpecification''] # 规格，药品信息、医用材料时一般不为空，其他为空
                  "deliverNum": business.data[''aux''][''prescriptionDetail''][_index_][''quantity''] # 发药数量，4位小数，发药数量，指多少个药品包装规格单位（包含饮片贴数）
                  "deliverNumUnit": business.data[''aux''][''prescriptionDetail''][_index_][''packageUnit''] # 数量单位，标准单位，发药数量单位
                  "money": business.data[''aux''][''prescriptionDetail''][_index_][''actualAmount''] # 金额，4位小数，该药品的单价*发药数量，计量单位为人民币元
                  "productFactory": business.data[''aux''][''prescriptionDetail''][_index_][''manufacturer''] # 药品厂家，如为药品，提供商品厂家名
                  "drugCategory": "T(java.util.Objects).equals(business.data[''data''][''medicineType''],0) ? ''01'' : ''03'' "  # 药品类别，字典映射：01- 西药；02- 中成药；03- 中药饮
    response:
      "infcode": "[''package''][''additionInfo''][errorCode]"
      "err_msg": "[''package''][''additionInfo''][errorMsg]"
      "electronicRxSn": "[''package''][''body''][0][''p_controlResultNo'']" # 电子处方平台流水号', false, '1', '2025-02-20 15:01:18', '1', '2025-04-11 16:55:47', false),
        (17, 1, 0, 3, 30003, 'Q300', '互联网监管-诊疗结算', 'dslType: contract
enable: true
name: 互联网诊疗结算
format: json
functions:
  - path: "''/rainbow/api/hpcp/hospolmonitor/settlement''"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "''Q300''"
          "body":
            - "medicalNum": business.data[''data''][''inquiryPref''] # 就诊流水号，医院门诊号，医疗机构内部门诊就诊唯一编号
              "billNum": business.data[''data''][''pref''] # 单据号，非空单据号并不是发票号，只是标识一个就诊流水号下一笔费用结算单据
              "medicalType": "''10''" # 医疗类别，字典映射 10-药店购药 11-普通门诊
              "treatDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''inquiryStartTime'']) # 就诊时间，格式YYYYMMDDHH24MISS，指的是患者实际就诊时间，不是HIS系统数据产生时间
              "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''dept_dict'',business.data[''data''][''deptPref''])" # 科室编码，非空字典映射，系统的科室编码
              "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDictLabel(business,''dept_dict'',business.data[''data''][''deptPref''],business.data[''data''][''deptName''])" # 科室名称，非空系统的科室名称
              "outpatientNumber": business.data[''aux''][''clinicalCase'']?.iptOtpNo # 门诊号，非空医院HIS系统中用来标识一次门诊
              "specialpatientID": "''0''" # 特殊患者标识，0:常规患者；1：高血压患者特病；2：糖尿病患者特病；3：血透析患者
              "reservationType": "''2''" # 预约来源类型，1:APP；2:网上；3:电话；4:其它预约；0：非预约
              "referral": business.data[''aux''][''clinicalCase'']?.referral # 转诊，3：基层医疗医疗机构转入；4：上级医疗机构转入；5：其它医疗机构转入；0:非转诊
              "siType": "''1''" # 医保类型，1,市职工医保；2，城乡居民；3，市内非医保；4，市外医保；5，市外非医保；6，离休干部
              "doctorCode": business.data[''aux'']?.get(''doctorInfo'')?.doctorHospitalPref # 诊断医师编号，必须和Q370中的医生编号相同
              "doctorName": business.data[''data''][''doctorName''] # 诊断医生姓名
              "credentialType": "''01''" # 证件类型，字典映射
              "credentialNum": business.data[''aux''][''inquiryDetailInfo'']?.patientIdCard # 证件号码
              "name": business.data[''data''][''fullName''] # 患者姓名，非空
              "gender": business.data[''data''][''patientSex''] # 患者性别，非空1：男性；2：女性；9：未说明性别
              "birthday": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data[''aux''][''inquiryDetailInfo'']?.patientIdCard)" # 出生日期，格式YYYYMMDD，非空
              "race": "''01''" # 民族，非空字典映射
              "sumMoney": business.data[''data''][''ext''][''pricingPrice''] # 费用总额，float(4)，非空2位小数
              "updateBy": business.data[''aux''][''operateUserInfo'']?.nickname # 经办人，非空医疗机构操作员姓名
              "settleDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''auditPrescriptionTime'']) # 结算时间，格式YYYYMMDDHH24MISS，非空
              "invoiceNO": business.data[''data''][''ext''][''setlInvoiceNumber''] # 发票号，非空票据上的发票号码
              "guardianName": business.data[''aux''][''inquiryDetailInfo'']?.ext?.guardianName # 患者监护人姓名，6岁以下患者就诊时必填
              "guardianIdType": "''01''" # 监护人证件类型，字典映射；6岁以下患者就诊时必填
              "guardianIdNo": business.data[''aux''][''inquiryDetailInfo'']?.ext?.guardianIdCard # 监护人证件号码，6岁以下患者就诊时必填
              "additionalDiagnosisList": # 诊断列表
                - "diagnosisCode": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisCode''] # 诊断编码，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisName": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisName''] # 诊断名称，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisClassify": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisClassify''],0) ? ''2'' : ''1''"  # 诊断分类，字典映射：1中医诊断，2西医诊断，中医病历时必有中医诊断和西医诊断
                  "diagnosisType": "''7''" # 诊断类型，字典映射，说明：医院诊断和对应的医保诊断都需要上传，如果非医保患者默认本地职工对应的诊断
                  "diagSort": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''index''] # 诊断排序
              "symptomCode": business.data[''aux''][''clinicalCase'']?.tcmSyndromeCode # 中医辨证编码，中医必填
              "symptomCodeName": business.data[''aux''][''clinicalCase'']?.tcmSyndromeName # 中医辨证名称，中医必填
              "composite": # 医保已支付费用列表 无值传 0.00
                - "selfCareAmount": "''0.00''" # 自理金额，2位小数，指乙类药品、诊疗项目、服务设施中个人按比例先行支付部分
                  "selfAmount": "''0.00''" # 自费金额，2位小数，指丙类药品、丙类诊疗项目、丙类服务设施和超限价部分
                  "inInsureMoney": "''0.00''" # 符合医保费用，2位小数，指的是符合基本医疗保险费用，在药品、诊疗项目和服务设施的甲类和乙类费用中刨除自理的费用，即总费用–自理自费
                  "medicareFundCost": "''0.00''" # 医保基金，所有医保基金支付总额，2位小数
                  "medicarePayLine": "''0.00''" # 医保起付线，本次就医的起付金，2位小数
                  "perBearMoney": "''0.00''" # 个人自付，符合医保费用中由个人支付的部分，包含起付标准，不包含转诊先自付，2位小数
                  "hosBearMoney": "''0.00''" # 医院负担，各别地方医保政策中需要医院负担的金额，2位小数
                  "priorBurdenMoney": "''0.00''" # 转诊先自付，患者从外地转入就诊，根据当地医保政策转外就诊需自付金额，2位小数
                  "sectionCoordinatePayMoney": "''0.00''" # 统筹分段自付，统筹分段计算的个人自付金额，2位小数
                  "overCappingPayMoney": "''0.00''" # 超封顶线自付，超过统筹封顶线自付金额，2位小数
                  "fundMoney": "''0.00''" # 统筹基金支付，根据人员身份进行填写（基本医疗保险基金支付、城镇居民医疗基金支付、新农合补偿金额），2位小数
                  "civilServantFundMoney": "''0.00''" # 公务员基金支付，公务员补充医疗保险支付金额，2位小数
                  "seriousFundMoney": "''0.00''" # 大病基金支付，大病基金支付金额，2位小数
                  "accountFundMoney": "''0.00''" # 账户支付，本次个人账户支付金额，2位小数
                  "civilSubsidy": "''0.00''" # 民政救助支付，民政救助金额，2位小数
                  "otherFundMoney": "''0.00''" # 其他基金支付，除过上述基金支付外的基金支付金额，2位小数
                  "cashMoney": business.data[''data''][''ext''][''pricingPrice''] # 本次现金支付，个人现金支付金额，2位小数
              "recipeList": # 医疗项目明细信息
                - "productName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 药品商品名，如为药品，提供商品名
                  "recipeSerialNum": _index_ # 同一个就诊下，处方流水号在中心端能够 唯一标识一条处方明细信息 目录类别为1，则上传处方明细流水号； 其余收费明细流水号
                  "listCat": "''1''" # 目录类别，非空1:药品；2:诊疗项目；3:服务设施；4:医用材料；5：转诊
                  "medicalItemCat": "T(java.util.Objects).equals(business.data[''data''][''medicineType''],0) ? ''01'' : ''03''" # 医疗项目类别，非空 字典映射
                  "recipeNum": business.data[''data''][''pref''] # 处方号，非空目录类别为1，则上传处方号；其余上传发票号；若没有发票号，则传单据号
                  "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''auditPrescriptionTime'']) # 收费日期，格式YYYYMMDDHH24MISS，非空
                  "productFactory": business.data[''aux''][''prescriptionDetail''][_index_][''manufacturer''] # 药品厂家，如为药品，提供商品厂家名
                  "hospitalChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 医院收费项目编码，非空
                  "hospitalChargeName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 医院收费项目名称，非空
                  "priceitemCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" #物价项目编码，非空物价局统一的医疗服务项目编码，如普通门诊诊察费：AAAA0001，不是医疗服务项目时，传医院收费编码
                  "centreChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 医保收费项目编码，非空本地就医时对应患者的医保编号，如目录类别是药品时，项目编码指的是药品编码；如果目录类别是诊疗项目时，项目编码为诊疗项目编码；如果目录类别为医用材料时，项目编码为医用材料编码。自费收费时，默认为本地城镇职工医保收费项目编码不在医保三大目录内的项目，比如伙食费、快递费等项目编码传PAXNBL0001
                  "medicareFeeitemName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 医保收费项目名称，社保经办机构三大目录管理规范名称，非空
                  "price": business.data[''aux''][''prescriptionDetail''][_index_][''productPrice''] # 单价，float(8)，非空4位小数
                  "quantity": business.data[''aux''][''prescriptionDetail''][_index_][''quantity''] # 数量，float(8)，非空4位小数，按照目录库中的包装上传入，非招标按照实际情况传入
                  "money": business.data[''aux''][''prescriptionDetail''][_index_][''actualAmount''] # 金额，float(8)，非空4位小数
                  "spec": business.data[''aux''][''prescriptionDetail''][_index_][''attributeSpecification''] # 规格
                  "standardUnit": business.data[''aux''][''prescriptionDetail''][_index_][''packageUnit''] # 标准单位，项目的包装单位或者计价单位
                  "perQuantity": business.data[''aux''][''prescriptionDetail''][_index_][''singleDose''] # 每次用量，4位小数，按照目录库中的最小单位数量
                  "frequency": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_use_frequency'',business.data[''aux''][''prescriptionDetail''][_index_][''useFrequency''])" # 使用频次，字典映射
                  "medicationRoute": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_directions'',business.data[''aux''][''prescriptionDetail''][_index_][''directions''])" # 给药途径
                  "drugDoseUnit": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_dose_unit'',business.data[''aux''][''prescriptionDetail''][_index_][''singleUnit''])" # 单次给药剂量单位，药品时需要填写，字典映射
                  "groupNo": "''1''" # 配伍组号，药品时需要填写
                  "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''dept_dict'',business.data[''data''][''deptPref''])" # 科室编码，非空字典映射，系统的科室编码
                  "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDictLabel(business,''dept_dict'',business.data[''data''][''deptPref''],business.data[''data''][''deptName''])" # 科室名称，非空系统的科室名称
                  "doctorCode": business.data[''aux'']?.get(''doctorInfo'')?.doctorHospitalPref # 诊断医师编号，必须和Q370中的医生编号相同
                  "doctorName": business.data[''data''][''doctorName''] # 诊断医生姓名
                  "recipedistribut": "''3''" # 处方配送标识，字典映射：1医院药房自取，2医院派送，3其他药店自取，4其他药店派送，不传默认为1
                  "keepUseFlag": "''1''" # 继用处方标识 1非继用，2继用
                  "selfPayRatio": "''100%''" # 自付比例
    response:
      "infcode": "[''package''][''additionInfo''][errorCode]"
      "err_msg": "[''package''][''additionInfo''][errorMsg]"
', false, '1', '2025-02-20 15:01:18', '1', '2025-04-11 16:57:18', false),
        (18, 1, 0, 3, 30004, 'Q430', '互联网监管-派药', 'dslType: contract
enable: true
name: 派药
format: json
functions:
  - path: "''/rainbow/api/hpcp/hospolmonitor/drugDelivery''"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "''Q430''"
          "body":
            - "medicalNum": business.data[''data''][''inquiryPref''] # 就诊流水号，由医院端上传，必须保证同一家医院的就诊流水号是唯一的
              "billNum": business.data[''data''][''pref''] # 单据号，单据号并不是发票号，只是标识一个就诊流水号下一笔费用结算单据
              "p_controlResultNo": business.data[''aux''][''30002''][''electronicRxSn''] # 电子处方平台流水号，市监管平台流水号
              "medicalType": "''10''" # 医疗类别，字典映射 10-药店购药 11-普通门诊
              "treatDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''inquiryStartTime'']) # 就诊时间，格式YYYYMMDDHH24MISS，指的是患者实际就诊时间，不是HIS系统数据产生时间
              "updateBy": business.data[''aux''][''operateUserInfo'']?.nickname # 经办人，医疗机构操作员姓名
              "invoiceNO": business.data[''data''][''ext''][''setlInvoiceNumber''] # 发票号，票据上的发票号码
              "recipeList": # 下方是处方明细信息
                - "productName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 药品商品名，如为药品，提供商品名
                  "recipeSerialNum": _index_ # 同一个就诊下，处方流水号在中心端能够 唯一标识一条处方明细信息 目录类别为1，则上传处方明细流水号； 其余收费明细流水号
                  "listCat": "''1''" # 目录类别，非空1:药品；2:诊疗项目；3:服务设施；4:医用材料；5：转诊
                  "medicalItemCat": "T(java.util.Objects).equals(business.data[''data''][''medicineType''],0) ? ''01'' : ''03''" # 医疗项目类别，非空 字典映射
                  "recipeNum": business.data[''data''][''pref''] # 处方号，非空目录类别为1，则上传处方号；其余上传发票号；若没有发票号，则传单据号
                  "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data[''data''][''auditPrescriptionTime'']) # 收费日期，格式YYYYMMDDHH24MISS，非空
                  "productFactory": business.data[''aux''][''prescriptionDetail''][_index_][''manufacturer''] # 药品厂家，如为药品，提供商品厂家名
                  "hospitalChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 医院收费项目编码，非空
                  "hospitalChargeName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 医院收费项目名称，非空
                  "priceitemCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 物价项目编码，非空物价局统一的医疗服务项目编码，如普通门诊诊察费：AAAA0001，不是医疗服务项目时，传医院收费编码
                  "centreChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data[''aux''][''prescriptionDetail''][_index_][''standardId''],business.data[''aux''][''prescriptionDetail''][_index_][''productPref''])" # 医保收费项目编码，非空本地就医时对应患者的医保编号，如目录类别是药品时，项目编码指的是药品编码；如果目录类别是诊疗项目时，项目编码为诊疗项目编码；如果目录类别为医用材料时，项目编码为医用材料编码。自费收费时，默认为本地城镇职工医保收费项目编码不在医保三大目录内的项目，比如伙食费、快递费等项目编码传PAXNBL0001
                  "medicareFeeitemName": business.data[''aux''][''prescriptionDetail''][_index_][''commonName''] # 医保收费项目名称，社保经办机构三大目录管理规范名称，非空
                  "price": business.data[''aux''][''prescriptionDetail''][_index_][''productPrice''] # 单价，float(8)，非空4位小数
                  "quantity": business.data[''aux''][''prescriptionDetail''][_index_][''quantity''] # 数量，float(8)，非空4位小数，按照目录库中的包装上传入，非招标按照实际情况传入
                  "money": business.data[''aux''][''prescriptionDetail''][_index_][''actualAmount''] # 金额，float(8)，非空4位小数
                  "spec": business.data[''aux''][''prescriptionDetail''][_index_][''attributeSpecification''] # 规格
                  "trialDoctorCode": business.data[''data''][''pharmacistPref''] # 审方药师编码
                  "trialDoctorName": business.data[''data''][''pharmacistName''] # 审方药师名称
                  "deliverPharmCode": business.data[''data''][''pharmacistPref''] # 发药药师编码
                  "deliverPharmName": business.data[''data''][''pharmacistName''] # 发药药师名称
                  "perQuantity": business.data[''aux''][''prescriptionDetail''][_index_][''singleDose''] # 每次用量，4位小数，按照目录库中的最小单位数量
                  "frequency": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_use_frequency'',business.data[''aux''][''prescriptionDetail''][_index_][''useFrequency''])" # 使用频次，字典映射
                  "medicationRoute": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_directions'',business.data[''aux''][''prescriptionDetail''][_index_][''directions''])" # 给药途径
                  "drugDoseUnit": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''drug_dose_unit'',business.data[''aux''][''prescriptionDetail''][_index_][''singleUnit''])" # 单次给药剂量单位，药品时需要填写，字典映射
                  "groupNo": "''1''" # 配伍组号，药品时需要填写
                  "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''dept_dict'',business.data[''aux''][''clinicalCase'']?.deptPref)" # 科室编码，非空字典映射，系统的科室编码
                  "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDictLabel(business,''dept_dict'',business.data[''aux''][''clinicalCase'']?.deptPref,business.data[''aux''][''clinicalCase'']?.deptName)" # 科室名称，非空系统的科室名称
                  "doctorCode": business.data[''aux'']?.get(''doctorInfo'')?.doctorHospitalPref # 诊断医师编号，必须和Q370中的医生编号相同
                  "doctorName": business.data[''data''][''doctorName''] # 诊断医生姓名
                  "recipeDocTitle": "T(cn.hutool.extra.spring.SpringUtil).getBean(''dictService'').convertOrganDict(business,''doctor_title'',business.data[''aux''][''doctorInfo'']?.titleCode)" # 开方医生职称，字典映射
                  "selfPayRatio": "''100%''" # 自付比例，如果医保已经支付，此处填写医保的乙类自付比例：丙类自费时100%，乙类按照实际比例传入；否则默认传入本地城镇职工医保的自付比例
                  "keepUseFlag": "''1''" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送
                  "recipedistribut": "''3''" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送
    response:
      "infcode": "[''package''][''additionInfo''][errorCode]"
      "err_msg": "[''package''][''additionInfo''][errorMsg]"', false, '1', '2025-02-20 15:01:18', '1', '2025-04-11 16:05:14', false),
        (19, 1, 0, 2, 30000, '', '互联网监管-在线挂号', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')
executionTime:
  onlyTask: true #仅上传任务
postParameter:
  nodes:
    - operateUserInfo
dependency:
  downstreamNodes:
    - 30001', false, '1', '2025-02-20 15:01:18', '1', '2025-04-02 15:45:12', false),
        (20, 1, 0, 3, 30000, 'Q299', '互联网监管-在线挂号', 'dslType: contract
enable: true
name: 在线挂号
format: json
functions:
  - path: "''/rainbow/api/hpcp/hospolmonitor/olregister''"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "''Q299''"
          "body":
            - "medicalNum": business.data[''data''][''inquiryPref''] # 就诊流水号，医院门诊号，医疗机构内部门诊就诊唯一编号
              "billNum": business.data[''data''][''inquiryPref''] # 单据号，非空单据号并不是发票号，只是标识一个就诊流水号下一笔费用结算单据
              "medicalType": "''10''" # 医疗类别，字典映射 10-药店购药 11-普通门诊
              "treatDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 就诊时间，格式YYYYMMDDHH24MISS，指的是患者实际就诊时间，不是HIS系统数据产生时间
              "deptNum": "''02''" # 科室编码，非空字典映射，系统的科室编码
              "deptName": "''全科医疗科''" # 科室名称，非空系统的科室名称
              "outpatientNumber": business.data[''data''][''patientPref''] # 门诊号，非空医院HIS系统中用来标识一次门诊
              "specialpatientID": "''0''" # 特殊患者标识，0:常规患者；1：高血压患者特病；2：糖尿病患者特病；3：血透析患者
              "reservationType": "''2''" # 预约来源类型，1:APP；2:网上；3:电话；4:其它预约；0：非预约
              "referral": "''0''" # 转诊，3：基层医疗医疗机构转入；4：上级医疗机构转入；5：其它医疗机构转入；0:非转诊
              "siType": "''1''" # 医保类型，1,市职工医保；2，城乡居民；3，市内非医保；4，市外医保；5，市外非医保；6，离休干部
              "doctorCode": "''D421087003352''" # 诊断医师编号
              "doctorName": "''肖治坤''" # 诊断医生姓名
              "credentialType": "''01''" # 证件类型，字典映射
              "credentialNum": business.data[''data''][''idCard''] # 证件号码
              "name": business.data[''data''][''fullName''] # 患者姓名，非空
              "gender": business.data[''data''][''patientSex''] # 患者性别，非空1：男性；2：女性；9：未说明性别
              "birthday": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data[''data''][''idCard''])" # 出生日期，格式YYYYMMDD，非空
              "race": "''01''" # 民族，非空字典映射
              "sumMoney": "0.00" # 费用总额，float(4)，非空2位小数
              "updateBy": business.data[''aux''][''operateUserInfo'']?.nickname # 经办人，非空医疗机构操作员姓名
              "settleDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 结算时间，格式YYYYMMDDHH24MISS，非空
              "invoiceNO": business.data[''data''][''inquiryPref''] # 发票号，非空票据上的发票号码
              "guardianName": "''''" # 患者监护人姓名，6岁以下患者就诊时必填
              "guardianIdType": "''''" # 监护人证件类型，字典映射；6岁以下患者就诊时必填
              "guardianIdNo": "''''" # 监护人证件号码，6岁以下患者就诊时必填
              "isKnowAgreePaper": "''1''" # 是否签署知情同意书 0否 1 是
              #            "additionalDiagnosisList": # 诊断列表
              #              - "diagnosisCode": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisCode''] # 诊断编码，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
              #                "diagnosisName": business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisName''] # 诊断名称，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
              #                "diagnosisClassify": "T(java.util.Objects).equals(business.data[''aux''][''clinicalCase'']?.diagnosis[_index_][''diagnosisClassify''],0) ? ''2'' : ''1''"  # 诊断分类，字典映射：1中医诊断，2西医诊断，中医病历时必有中医诊断和西医诊断
              #                "diagnosisType": "''7''" # 诊断类型，字典映射，说明：医院诊断和对应的医保诊断都需要上传，如果非医保患者默认本地职工对应的诊断
              #                "diagSort": _index_ # 诊断排序，诊断排序：0、主要诊断，1、次要诊断1,2、次要诊断2，等，排序从0开始
              "composite": # 医保已支付费用列表 无值传 0.00
                - "selfCareAmount": "''0.00''" # 自理金额，2位小数，指乙类药品、诊疗项目、服务设施中个人按比例先行支付部分
                  "selfAmount": "''0.00''" # 自费金额，2位小数，指丙类药品、丙类诊疗项目、丙类服务设施和超限价部分
                  "inInsureMoney": "''0.00''" # 符合医保费用，2位小数，指的是符合基本医疗保险费用，在药品、诊疗项目和服务设施的甲类和乙类费用中刨除自理的费用，即总费用–自理自费
                  "medicareFundCost": "''0.00''" # 医保基金，所有医保基金支付总额，2位小数
                  "medicarePayLine": "''0.00''" # 医保起付线，本次就医的起付金，2位小数
                  "perBearMoney": "''0.00''" # 个人自付，符合医保费用中由个人支付的部分，包含起付标准，不包含转诊先自付，2位小数
                  "hosBearMoney": "''0.00''" # 医院负担，各别地方医保政策中需要医院负担的金额，2位小数
                  "priorBurdenMoney": "''0.00''" # 转诊先自付，患者从外地转入就诊，根据当地医保政策转外就诊需自付金额，2位小数
                  "sectionCoordinatePayMoney": "''0.00''" # 统筹分段自付，统筹分段计算的个人自付金额，2位小数
                  "overCappingPayMoney": "''0.00''" # 超封顶线自付，超过统筹封顶线自付金额，2位小数
                  "fundMoney": "''0.00''" # 统筹基金支付，根据人员身份进行填写（基本医疗保险基金支付、城镇居民医疗基金支付、新农合补偿金额），2位小数
                  "civilServantFundMoney": "''0.00''" # 公务员基金支付，公务员补充医疗保险支付金额，2位小数
                  "seriousFundMoney": "''0.00''" # 大病基金支付，大病基金支付金额，2位小数
                  "accountFundMoney": "''0.00''" # 账户支付，本次个人账户支付金额，2位小数
                  "civilSubsidy": "''0.00''" # 民政救助支付，民政救助金额，2位小数
                  "otherFundMoney": "''0.00''" # 其他基金支付，除过上述基金支付外的基金支付金额，2位小数
                  "cashMoney": "''0.00''" # 本次现金支付，个人现金支付金额，2位小数
              "recipeList": # 医疗项目明细信息
                - "recipeSerialNum": business.data[''data''][''inquiryPref''] # 同一个就诊下，处方流水号在中心端能够 唯一标识一条处方明细信息 目录类别为1，则上传处方明细流水号； 其余收费明细流水号
                  "listCat": "''2''" # 目录类别，非空1:药品；2:诊疗项目；3:服务设施；4:医用材料；5：转诊
                  "medicalItemCat": "''27''" # 医疗项目类别，字典映射挂号-27
                  "recipeNum": business.data[''data''][''inquiryPref''] # 处方号，非空目录类别为1，则上传处方号；其余上传发票号；若没有发票号，则传单据号
                  "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 收费日期，格式YYYYMMDDHH24MISS，非空
                  "hospitalChargeCode": "''t4y8eashpsijj3kz8dx6b4by43kjkhp5''" # 医院收费项目编码，非空
                  "hospitalChargeName": "''在线复诊服务''" # 医院收费项目名称，非空
                  "priceitemCode": "''104541''" # 物价项目编码，非空物价局统一的医疗服务项目编码，如普通门诊诊察费：AAAA0001，不是医疗服务项目时，传医院收费编码
                  "centreChargeCode": "''501110000050000-111101001.20''" # 医保收费项目编码，非空 本地就医时对应患者的医保编号，如目录类别是药品时，项目编码指的是药品编码；如果目录类别是诊疗项目时，项目编码为诊疗项目编码；如果目录类别为医用材料时，项目编码为医用材料编码。自费收费时，默认为本地城镇职工医保收费项目编码不在医保三大目录内的项目，比如伙食费、快递费等项目编码传PAXNBL0001
                  "medicareFeeitemName": "''互联网复诊费（二级医院）''" # 医保收费项目名称，社保经办机构三大目录管理规范名称，非空
                  "price": "''0.00''" # 单价，float(8)，非空4位小数
                  "quantity": "''1.00''" # 数量，float(8)，非空4位小数，按照目录库中的包装上传入，非招标按照实际情况传入
                  "money": "''0.00''" # 金额，float(8)，非空4位小数
                  "deptNum": "''50''" # 科室编码，非空字典映射，系统的科室编码
                  "deptName": "''中医科''" # 科室名称，非空系统的科室名称
                  "keepUseFlag": "''1''" # 继用处方标识 1非继用，2继用
                  "selfPayRatio": "''100%''" # 自付比例
    response:
      "infcode": "[''package''][''additionInfo''][errorCode]"
      "err_msg": "[''package''][''additionInfo''][errorMsg]"', false, '1', '2025-02-20 15:01:18', '1', '2025-03-31 16:08:23', false),
        (21, 1, 0, 2, 29999, '', '互联网监管-监管条件', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data[''hospitalPref''],''H100008'',''H100009'')
pricingHospitalPref: H100008,H100009
preParameter:
  nodes:
    - prescriptionCa
preParameterFilter:
  condition: T(cn.hutool.core.collection.CollUtil).size(aux[''prescriptionCa'']) >= 2', false, '1', '2025-02-20 15:01:18', '1', '2025-04-02 15:45:12', false),
        (1110, 663, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1111, 663, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1112, 663, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1113, 663, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1114, 663, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1115, 663, 0, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-01 10:46:32', '1', '2025-04-01 10:46:32', false),
        (1116, 664, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1117, 664, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1118, 664, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1119, 664, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1120, 664, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1121, 664, 0, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-01 10:46:36', '1', '2025-04-01 10:46:36', false),
        (1122, 665, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1123, 665, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1124, 665, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1125, 665, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1126, 665, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1127, 665, 0, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-01 11:44:09', '1', '2025-04-01 11:44:09', false),
        (1128, 666, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-01 13:19:57', '1', '2025-04-02 14:38:50', false),
        (1129, 666, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-01 13:19:57', '1', '2025-04-01 13:19:57', false),
        (1130, 666, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-01 13:19:57', '1', '2025-04-01 13:19:57', false),
        (1131, 666, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-01 13:19:57', '1', '2025-04-01 13:19:57', false),
        (1132, 666, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-01 13:19:57', '1', '2025-04-01 13:19:57', false),
        (1133, 666, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-01 13:19:57', '1', '2025-04-02 17:45:47', false),
        (1134, 667, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 11:34:06', '1', '2025-04-03 11:34:06', false),
        (1135, 667, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1136, 667, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1137, 667, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1138, 667, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1139, 667, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1140, 668, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1141, 668, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 11:34:07', '1', '2025-04-03 11:34:07', false),
        (1142, 668, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 11:34:08', '1', '2025-04-03 11:34:08', false),
        (1143, 668, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 11:34:08', '1', '2025-04-03 11:34:08', false),
        (1144, 668, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 11:34:08', '1', '2025-04-03 11:34:08', false),
        (1145, 668, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 11:34:08', '1', '2025-04-03 11:34:08', false),
        (1146, 669, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1147, 669, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1148, 669, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1149, 669, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1150, 669, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1151, 669, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 11:34:09', '1', '2025-04-03 11:34:09', false),
        (1152, 670, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', true, '1', '2025-04-03 11:34:10', '1', '2025-04-07 15:22:12', false),
        (1153, 670, 1154, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', true, '1', '2025-04-03 11:34:10', '1', '2025-04-07 14:46:59', false),
        (1154, 670, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 11:34:10', '1', '2025-04-03 11:34:10', false),
        (1155, 670, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 11:34:10', '1', '2025-04-03 11:34:10', false),
        (1156, 670, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 11:34:10', '1', '2025-04-03 11:34:10', false),
        (1157, 670, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 11:34:10', '1', '2025-04-03 11:34:10', false),
        (1158, 671, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1159, 671, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1160, 671, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1161, 671, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1162, 671, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1163, 671, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 13:03:50', '1', '2025-04-03 13:03:50', false),
        (1164, 672, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1165, 672, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1166, 672, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1167, 672, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1168, 672, 0, 3, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1169, 672, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 13:03:51', '1', '2025-04-03 13:03:51', false),
        (1170, 673, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-03 13:12:41', '1', '2025-04-03 13:12:41', false),
        (1171, 673, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-03 13:12:41', '1', '2025-04-03 13:12:41', false),
        (1172, 673, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-03 13:12:41', '1', '2025-04-03 13:12:41', false),
        (1173, 673, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-03 13:12:41', '1', '2025-04-03 13:12:41', false),
        (1174, 673, 0, 2, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', true, '1', '2025-04-03 13:12:41', '1', '2025-04-07 15:01:23', false),
        (1175, 673, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-03 13:12:41', '1', '2025-04-03 13:12:41', false),
        (1176, 670, 1153, 1, 40005, '212112', 'HIS对接-更改预约订单状态', null, true, '1', '2025-04-07 15:04:20', '1', '2025-04-07 15:04:20', false),
        (1177, 673, 1172, 2, 30002, '', '互联网监管-线上处方点评', null, true, '1', '2025-04-07 15:05:41', '1', '2025-04-07 17:38:28', true),
        (1178, 673, 1172, 1, 30004, '', '互联网监管-派药', null, true, '1', '2025-04-07 15:12:32', '1', '2025-04-07 16:28:38', true),
        (1179, 670, 0, 1, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', true, '1', '2025-04-07 15:22:22', '1', '2025-04-07 15:22:22', false),
        (1180, 673, 1178, 2, 30004, '', '互联网监管-派药', null, true, '1', '2025-04-07 15:26:58', '1', '2025-04-07 16:28:33', true),
        (1181, 674, null, 3, 30001, '123123', '互联网监管-上传门诊病例', null, true, '1', '2025-04-08 15:25:52', '1', '2025-04-08 15:38:55', false),
        (1182, 679, 0, 2, 40001, '', 'HIS对接-挂号信息查询', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equals(data[''hospitalPref''],''H100008'')
supervisionHospitalPref: H100008', false, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1183, 679, 0, 2, 40005, '', 'HIS对接-更改预约订单状态', 'preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).contains([''hospitalPref''],''H100008'')', false, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1184, 679, 0, 3, 40001, '', 'HIS对接-挂号信息查询', 'dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''''" # 项目ID
        "idCardNo": business.data[''data''][''idCardNo'']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "[''errcode'']"
      "msg": "[''errmsg'']"
      "bizVisitId": "[''bizData''][0][''bookId'']"
      "medicalVisitId": "[''bizData''][0][''chsVisitId'']"
      "medicalVisitDate": "[''bizData''][0][''chsRegistTime'']"
      "medType": "[''bizData''][0][''medType'']"
      "insuredAreaNo": "[''bizData''][0][''insuPlcNo'']"
      "tenantAreaNo": "[''bizData''][0][''mdtrtareaNo'']"
      "psnNo": "[''bizData''][0][''psnNo'']"
      "certno": "[''bizData''][0][''certno'']"
      "patientName": "[''bizData''][0][''patnName'']"
      "patientMobile": "[''bizData''][0][''phone'']"
      "bookTime": "[''bizData''][0][''bookTime'']"
      "planTime": "[''bizData''][0][''planDate'']"
      "deptName": "[''bizData''][0][''prscDeptName'']"
      "iptOtpNo": "[''bizData''][0][''iptOtpNo'']"
      "status": "[''bizData''][0][''status'']"
', false, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1185, 679, 0, 3, 9999902, '', 'HIS对接-获取Token', 'name: ''获取token''
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request: 
      format: form
      body:
        "appId": "''''" #应用id
        "appSecret": "''''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "[''accessToken'']"
clearContext: false
result:
  success: output[''infcode''] == 0
  skip: false
  tips: output[''msg'']', false, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1186, 679, 0, 2, 9999901, '', 'HIS-公共节点', 'header:
  "Authorization": "''Bearer '' + T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output[''infcode''] == 0 || output[''infcode''] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove(''accessToken'') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean(''tokenService'').getToken({''refresh'':true})"', true, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1187, 679, 1133, 3, 40005, '', 'HIS对接-更改预约订单状态', 'dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data[''data''][''medicalVisitId''] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data[''data''][''status''],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明', false, '1', '2025-04-08 15:46:11', '1', '2025-04-08 15:46:11', false),
        (1188, 684, null, 1, 9999901, '1111', '公共节点', null, false, '1', '2025-04-09 11:37:47', '1', '2025-04-09 11:41:19', false),
        (1189, 684, null, 2, 9999901, '1111', '公共节点', null, false, '1', '2025-04-09 13:50:12', '1', '2025-04-09 13:50:12', false),
        (1190, 684, null, 3, 9999901, '1111', '公共节点', null, false, '1', '2025-04-09 13:50:19', '1', '2025-04-09 13:50:19', false),
        (1191, 684, null, 1, 9999902, '222', '获取token', null, false, '1', '2025-04-09 13:50:29', '1', '2025-04-09 15:47:51', false),
        (1192, 684, null, 3, 9999902, '222', '获取token', null, false, '1', '2025-04-09 17:30:09', '1', '2025-04-09 17:30:09', false),
        (1193, 684, null, 3, 9999902, '222', '互联网监管-派药', null, false, '1', '2025-04-09 17:32:54', '1', '2025-04-09 17:47:04', true),
        (1194, 684, null, 3, 9999902, '222', '互联网监管-诊疗结算', null, false, '1', '2025-04-09 17:34:43', '1', '2025-04-09 17:41:11', true),
        (1195, 684, null, 3, 9999902, '222', '互联网监管-派药', null, false, '1', '2025-04-09 17:48:41', '1', '2025-04-09 17:53:45', true),
        (1196, 684, 1192, 3, 9999902, '222', '互联网监管-派药', null, false, '1', '2025-04-09 17:54:09', '1', '2025-04-09 20:40:25', false),
        (1197, 684, null, 3, 9999902, '222', '互联网监管-诊疗结算', null, false, '1', '2025-04-09 17:54:36', '1', '2025-04-09 17:55:14', true),
        (1198, 684, null, 3, 9999902, '222', '互联网监管-诊疗结算', null, false, '1', '2025-04-09 17:55:20', '1', '2025-04-09 17:55:20', false),
        (1199, 692, null, 1, 30004, '', '互联网监管-派药', null, false, '1', '2025-04-10 13:29:45', '1', '2025-04-10 13:32:23', false),
        (1200, 696, null, 3, 30001, '123123', '互联网监管-上传门诊病例', null, true, '1', '2025-04-10 19:54:35', '1', '2025-04-10 19:54:35', false),
        (1201, 698, null, 1, 9999901, '', '公共节点', null, false, '1', '2025-04-10 20:03:11', '1', '2025-04-10 20:03:11', false),
        (1202, 699, null, 1, 9999901, '', '公共节点', null, false, '1', '2025-04-10 20:16:07', '1', '2025-04-10 20:18:17', false),
        (1203, 699, null, 1, 30000, '', '互联网监管-在线挂号', null, false, '1', '2025-04-10 20:18:25', '1', '2025-04-10 20:18:25', false),
        (1204, 700, null, 1, 9999901, '', '公共节点', null, false, '1', '2025-04-11 13:49:52', '1', '2025-04-11 13:49:52', false),
        (1205, 700, null, 1, 30000, '', '互联网监管-在线挂号', null, false, '1', '2025-04-11 13:49:52', '1', '2025-04-11 13:49:52', false),
        (1206, 701, null, 3, 30001, '123123', '互联网监管-上传门诊病例', null, true, '1', '2025-04-11 13:50:07', '1', '2025-04-11 13:50:07', false),
        (1207, 702, null, 1, 9999901, '', '公共节点', '24', false, '1', '2025-04-14 10:20:44', '1', '2025-04-14 10:20:44', false),
        (1208, 705, null, 3, 29999, '1003', '互联网监管-基础监管条件', 'werwetyret', false, '1', '2025-04-14 10:31:21', '1', '2025-04-14 10:31:21', false),
        (1209, 705, null, 2, 9999902, '1002', '获取token', 'werwr', false, '1', '2025-04-14 10:31:23', '1', '2025-04-14 10:31:23', false),
        (1210, 705, null, 1, 9999901, '1001', '公共节点', 'rewtfwgt', false, '1', '2025-04-14 10:31:24', '1', '2025-04-14 10:31:24', false),
        (1211, 705, null, 1, 30000, '1004', '互联网监管-在线挂号', 'werwetwt', false, '1', '2025-04-14 10:31:31', '1', '2025-04-14 10:31:31', false),
        (1212, 705, null, 1, 30001, '1005', '互联网监管-上传门诊病例', '11111111111111', false, '1', '2025-04-14 10:31:51', '1', '2025-04-14 10:31:51', false),
        (1213, 705, null, 2, 30002, '1006', '互联网监管-线上处方点评', '122222222222222222', false, '1', '2025-04-14 10:31:52', '1', '2025-04-14 10:31:52', false),
        (1214, 705, null, 1, 30003, '1007', '互联网监管-诊疗结算', '333333333333', false, '1', '2025-04-14 10:32:14', '1', '2025-04-14 10:32:14', false),
        (1215, 705, null, 3, 30004, '1008', '互联网监管-派药', '4444444', false, '1', '2025-04-14 10:32:15', '1', '2025-04-14 10:32:15', false),
        (1216, 706, null, 3, 29999, '1003', '互联网监管-基础监管条件', 'werwetyret', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1217, 706, null, 2, 9999902, '1002', '获取token', 'werwr', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1218, 706, null, 1, 9999901, '1001', '公共节点', 'rewtfwgt', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1219, 706, null, 1, 30000, '1004', '互联网监管-在线挂号', 'werwetwt', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1220, 706, null, 1, 30001, '1005', '互联网监管-上传门诊病例', '11111111111111', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1221, 706, null, 2, 30002, '1006', '互联网监管-线上处方点评', '122222222222222222', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1222, 706, null, 1, 30003, '1007', '互联网监管-诊疗结算', '333333333333', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1223, 706, null, 3, 30004, '1008', '互联网监管-派药', '4444444', false, '1', '2025-04-15 17:42:48', '1', '2025-04-15 17:42:48', false),
        (1224, 706, null, 2, 9999901, '', '公共节点', null, false, '1', '2025-04-15 17:43:15', '1', '2025-04-15 17:43:15', false),
        (1225, 709, null, 1, 9999901, '1004', '公共节点', null, false, '1', '2025-04-15 22:09:02', '1', '2025-04-15 22:09:02', false),
        (1226, 709, null, 3, 30004, '1006', '互联网监管-派药', null, false, '1', '2025-04-15 22:09:11', '1', '2025-04-15 22:09:12', false),
        (1227, 710, null, 1, 29999, '19999', '互联网监管-基础监管条件', null, false, '1', '2025-04-15 22:10:24', '1', '2025-04-15 22:10:24', false),
        (1228, 711, null, 1, 29999, '19999', '互联网监管-基础监管条件', null, false, '1', '2025-06-10 17:52:56', '1', '2025-06-10 17:52:56', false);