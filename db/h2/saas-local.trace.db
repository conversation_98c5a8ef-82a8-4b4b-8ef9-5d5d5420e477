2025-07-01 16:20:29.224184+08:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "ACT_RU_JOB" not found (this database is empty); SQL statement:
SELECT RES.*  
         
        from ACT_RU_JOB RES
         WHERE  RES.LOCK_OWNER_ = ? 
     
        order by RES.ID_ asc [42104-224]
2025-07-01 16:20:29.230847+08:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "ACT_RU_JOB" not found (this database is empty); SQL statement:
SELECT RES.*  
         
        from ACT_RU_JOB RES
         WHERE  RES.LOCK_OWNER_ = ? 
     
        order by RES.ID_ asc [42104-224]
2025-07-01 08:20:43.222742Z jdbc[5]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "CATALOGS" not found; SQL statement:
select CATALOG_NAME from INFORMATION_SCHEMA.CATALOGS [42102-232]
2025-07-01 21:14:25.446100+08:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.getAutoCommit(JdbcConnection.java:465)
	at com.zaxxer.hikari.pool.HikariProxyConnection.getAutoCommit(HikariProxyConnection.java)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:286)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:604)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:373)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.xyy.saas.datasync.client.db.DataSyncPersistencer$$SpringCGLIB$$0.pullToPersistence(<generated>)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:84)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:64)
2025-07-01 21:14:25.461949+08:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.clearWarnings(JdbcConnection.java:660)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:258)
	at org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection(DataSourceUtils.java:406)
	at org.springframework.jdbc.datasource.DataSourceUtils.doReleaseConnection(DataSourceUtils.java:393)
	at org.springframework.jdbc.datasource.DataSourceUtils.releaseConnection(DataSourceUtils.java:360)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:310)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:604)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:373)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.xyy.saas.datasync.client.db.DataSyncPersistencer$$SpringCGLIB$$0.pullToPersistence(<generated>)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:84)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:64)
2025-07-02 17:21:02.281246+08:00 jdbc[45]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: The database is open in exclusive mode; can not open additional connections [90135-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.engine.SessionLocal.checkSuspended(SessionLocal.java:1254)
	at org.h2.engine.SessionLocal.transitionToState(SessionLocal.java:1246)
	at org.h2.engine.SessionLocal.setCurrentCommand(SessionLocal.java:1224)
	at org.h2.engine.SessionLocal.endStatement(SessionLocal.java:1707)
	at org.h2.command.Command.executeQuery(Command.java:229)
	at org.h2.jdbc.JdbcStatement.executeQuery(JdbcStatement.java:101)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:463)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:519)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:526)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:386)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:35)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:64)
2025-07-02 17:21:02.526286+08:00 jdbc[45]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.clearWarnings(JdbcConnection.java:660)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:258)
	at org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection(DataSourceUtils.java:406)
	at org.springframework.jdbc.datasource.DataSourceUtils.doReleaseConnection(DataSourceUtils.java:393)
	at org.springframework.jdbc.datasource.DataSourceUtils.releaseConnection(DataSourceUtils.java:360)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:406)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:519)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:526)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:386)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:35)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:64)
2025-07-29 09:37:55.297954+08:00 jdbc[44]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: The database is open in exclusive mode; can not open additional connections [90135-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.engine.SessionLocal.checkSuspended(SessionLocal.java:1254)
	at org.h2.engine.SessionLocal.transitionToState(SessionLocal.java:1246)
	at org.h2.engine.SessionLocal.setCurrentCommand(SessionLocal.java:1224)
	at org.h2.engine.SessionLocal.endStatement(SessionLocal.java:1707)
	at org.h2.command.Command.executeQuery(Command.java:229)
	at org.h2.jdbc.JdbcStatement.executeQuery(JdbcStatement.java:101)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:463)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:519)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:526)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:382)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:35)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
2025-07-29 09:37:56.414958+08:00 jdbc[44]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.clearWarnings(JdbcConnection.java:660)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:258)
	at org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection(DataSourceUtils.java:406)
	at org.springframework.jdbc.datasource.DataSourceUtils.doReleaseConnection(DataSourceUtils.java:393)
	at org.springframework.jdbc.datasource.DataSourceUtils.releaseConnection(DataSourceUtils.java:360)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:406)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:519)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:526)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:382)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:35)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.scheduleNextTask(DataSyncTask.java:97)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:77)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
2025-07-29 09:37:56.623522+08:00 jdbc[44]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.getAutoCommit(JdbcConnection.java:465)
	at com.zaxxer.hikari.pool.HikariProxyConnection.getAutoCommit(HikariProxyConnection.java)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:286)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:604)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:373)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.xyy.saas.inquiry.localmock.mq.LocalMqDbConsumer$$SpringCGLIB$$0.pollAndConsume(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:499)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:227)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:677)
	at org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.scheduleWithFixedDelay(ThreadPoolTaskScheduler.java:439)
	at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleWithFixedDelay(TaskSchedulerRouter.java:131)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedDelayTask(ScheduledTaskRegistrar.java:585)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:456)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:267)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:677)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:111)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:993)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:628)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
2025-07-29 09:37:56.816334+08:00 jdbc[44]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.clearWarnings(JdbcConnection.java:660)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:258)
	at org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection(DataSourceUtils.java:406)
	at org.springframework.jdbc.datasource.DataSourceUtils.doReleaseConnection(DataSourceUtils.java:393)
	at org.springframework.jdbc.datasource.DataSourceUtils.releaseConnection(DataSourceUtils.java:360)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:310)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:604)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:373)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.xyy.saas.inquiry.localmock.mq.LocalMqDbConsumer$$SpringCGLIB$$0.pollAndConsume(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:499)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:227)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:677)
	at org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.scheduleWithFixedDelay(ThreadPoolTaskScheduler.java:439)
	at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleWithFixedDelay(TaskSchedulerRouter.java:131)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedDelayTask(ScheduledTaskRegistrar.java:585)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:456)
	at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:267)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:677)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:111)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:993)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:628)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
2025-07-29 09:43:21.570444+08:00 jdbc[4]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:314)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:328)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.springframework.jdbc.core.JdbcTemplate$SimplePreparedStatementCreator.createPreparedStatement(JdbcTemplate.java:1711)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:656)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:754)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:767)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:889)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:916)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.checkTableColumnExist(H2DataTableDmlTransformer.java:366)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:377)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:95)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
2025-07-29 09:43:21.620747+08:00 jdbc[4]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1425)
	at org.h2.jdbc.JdbcConnection.clearWarnings(JdbcConnection.java:660)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:258)
	at org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection(DataSourceUtils.java:406)
	at org.springframework.jdbc.datasource.DataSourceUtils.doReleaseConnection(DataSourceUtils.java:393)
	at org.springframework.jdbc.datasource.DataSourceUtils.releaseConnection(DataSourceUtils.java:360)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:675)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:754)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:767)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:889)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:916)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.checkTableColumnExist(H2DataTableDmlTransformer.java:366)
	at com.xyy.saas.datasync.client.db.database.H2DataTableDmlTransformer.getMaxBaseVersion(H2DataTableDmlTransformer.java:377)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:95)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.concurrent.Executors$DelegatedExecutorService.submit(Executors.java:785)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.pullInvoker(SingleThreadPullSubscriberExecutor.java:37)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.lambda$initScheduledTasks$0(DataSyncClientWorker.java:66)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.run(DataSyncTask.java:66)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:215)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:561)
	at java.base/java.util.concurrent.Executors$DelegatedScheduledExecutorService.schedule(Executors.java:864)
	at com.xyy.saas.datasync.client.worker.DataSyncTask.start(DataSyncTask.java:55)
	at com.xyy.saas.datasync.client.worker.DataSyncClientWorker.handleInitializationAfterPulled(DataSyncClientWorker.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:365)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:237)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:168)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:349)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:65)
