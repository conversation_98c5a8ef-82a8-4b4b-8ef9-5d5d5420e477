# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Spring Boot 的多模块医疗 SaaS 系统，主要包含问诊服务和本地服务器组件。项目采用微服务架构，支持药店端到云端的完整解决方案。

## 核心架构

### 主要模块结构
- **saas-inquiry-kernel**: 问诊系统核心模块，包含医院、药店、患者、医生、药师、即时通讯等子模块
- **saas-inquiry-system**: 问诊系统业务模块，包含用户管理和数据传输服务
- **saas-localserver**: 本地服务器模块，处理药店本地业务逻辑
- **saas-localserver-cloud**: 云端服务模块，提供云端业务处理能力
- **local-server-cloud**: 基础云服务组件，包含数据同步、设备管理、网络代理等

### 关键技术栈
- Java 21
- Spring Boot 3.3.4
- MyBatis Plus 3.5.7
- Dubbo 3.3.0 (微服务通信)
- Nacos (服务注册与配置中心)
- H2/SQLite (本地数据库)
- RocketMQ (消息队列)
- 芋道框架 (yudao-framework)

## 常用构建命令

### 项目构建
```bash
# 构建所有模块 (推荐使用项目根目录的构建脚本)
./build.sh

# 仅安装 (跳过测试)
./build.sh install

# 仅部署
./build.sh deploy

# 指定环境部署
./build.sh deploy -P prod

# 手动构建 (如果需要)
mvn clean install -Dmaven.test.skip=true
```

### 模块特定构建
```bash
# 构建问诊系统 (排除启动应用)
cd saas-inquiry-kernel
mvn clean install -Dmaven.test.skip=true -pl '!saas-inquiry-kernel-all'

# 构建本地服务器 (排除应用入口)
cd saas-localserver  
mvn clean install -Dmaven.test.skip=true -pl '!saas-localserver-application'

# 构建云端服务 (排除云端应用)
cd saas-localserver-cloud
mvn clean install -Dmaven.test.skip=true -pl '!saas-cloudserver-application'
```

### 测试命令
```bash
# 运行单元测试
mvn test

# 生成测试覆盖率报告 (问诊模块)
mvn clean test -P jacoco
```

## 启动服务

### 主要应用启动类
- **问诊系统完整服务**: `com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication` (端口: 48082)
- **本地服务器**: `com.xyy.saas.localserver.LocalserverApplication` (端口: 8071)
- **云端服务**: `com.xyy.saas.cloudserver.CloudServerApplication`
- **数据同步服务**: `com.xyy.saas.datasync.server.DataSyncServerApplication`

### 环境配置
- 开发环境: `spring.profiles.active=dev`
- 测试环境: `spring.profiles.active=test` 
- 生产环境: `spring.profiles.active=prod`

## 开发指南

### 数据库配置
- **本地服务器**: 使用 H2 数据库 (`jdbc:h2:file:${user.dir}/db/h2/saas-local`)
- **问诊系统**: 支持 MySQL (通过 Nacos 配置中心动态配置)
- **数据迁移**: 使用 Flyway 进行数据库版本管理

### 多租户支持
项目支持多租户架构，门店作为租户单位：
- 租户配置在 `yudao.tenant` 下
- 忽略租户的表在配置中明确列出
- API 路径中可以忽略租户验证的接口需要在 `ignore-urls` 中配置

### 医保 DSL 配置
本地服务器模块 (`saas-localserver-dsl`) 支持通过 YAML 配置动态调用第三方医保引擎，支持多种协议客户端 (HTTP、DLL、WebService)。

### 服务通信
- **内部服务间**: 使用 Dubbo 进行 RPC 调用
- **外部系统**: 使用 REST API
- **消息队列**: 使用 RocketMQ 处理异步消息

### 代码规范
- 遵循《阿里巴巴Java开发手册》规范
- 使用 Lombok 简化代码
- 使用 MapStruct 进行对象映射
- 重要功能需要编写单元测试

### 安全配置
- 使用 JWT Token 进行身份认证
- 支持微信公众号和小程序登录
- 腾讯 IM 和 TRTC 集成用于即时通讯和音视频服务

## 部署说明

### 环境要求
- Java 21+
- Maven 3.6+
- 内部 Maven 仓库访问权限

### 部署环境
- **测试环境**: `mvn.int.ybm100.com`
- **生产环境**: `maven.int.ybm100.com`

### 构建产物
所有模块使用统一版本号 `2.0.0-SNAPSHOT`，通过 `flatten-maven-plugin` 管理版本。

## 特殊配置

### 数据同步
项目支持端到云、云到端、端到端的数据同步机制，通过 `local-server-cloud/data-pipeline` 模块实现。

### 即时通讯
集成腾讯 IM SDK，支持医患之间的实时通讯功能。

### 签章服务
通过 `saas-inquiry-signature` 模块提供电子签章功能，支持处方笺的数字化签名。