package com.xyy.saas.cloudserver;

import cn.iocoder.yudao.framework.idempotent.config.YudaoIdempotentConfiguration;
import cn.iocoder.yudao.framework.mq.redis.config.YudaoRedisMQProducerAutoConfiguration;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.ratelimiter.config.YudaoRateLimiterConfiguration;
import cn.iocoder.yudao.framework.redis.config.YudaoRedisAutoConfiguration;
import cn.iocoder.yudao.framework.security.config.AuthorizeRequestsCustomizer;
import cn.iocoder.yudao.framework.security.config.YudaoWebSecurityConfigurerAdapter;
import cn.iocoder.yudao.framework.signature.config.YudaoApiSignatureAutoConfiguration;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration;
import com.binarywang.spring.starter.wxjava.miniapp.config.WxMaServiceAutoConfiguration;
import com.binarywang.spring.starter.wxjava.mp.config.WxMpAutoConfiguration;
import com.xyy.common.config.TomcatServerConfig;
import com.xyy.saas.cloudserver.config.db.TenantIdentifierGenerator;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.inquiry.config.auto.MultiEnvAutoConfiguration;
import com.xyy.saas.inquiry.config.servlet.TomcatServerConfiguration;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * @Desc 云端服务
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/4/30 下午2:26
 */
//
//@SpringBootApplication(
//        scanBasePackages = {"${yudao.info.base-package}.server",
//                "${yudao.info.base-package}.module", "com.xyy.saas.datasync.server.controller",
//                "com.xyy.saas.localserver",}, exclude = {
//        RedisAutoConfiguration.class,
//        YudaoIdempotentConfiguration.class,
//        YudaoRedisAutoConfiguration.class,
//        DubboAutoConfiguration.class,
//        YudaoRateLimiterConfiguration.class,
//        YudaoApiSignatureAutoConfiguration.class,
////        YudaoMQAutoConfiguration.class,
////    YudaoRedisMQAutoConfiguration.class,
////    RedissonAutoConfiguration.class,
//        RedissonAutoConfigurationV2.class})
//@Slf4j
//@EnableDataSyncScan(
//        syncType = EnableDataSyncScan.SyncType.server,
//        scanBasePackages = "cn.iocoder.yudao.module.*.dal.dataobject.*",
//        excludePackages = "cn.iocoder.yudao.module.infra.dal.dataobject.demo",
//        baseEntity = {cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO.class})

// 和 saas-localserver-application 保持一致
@SpringBootApplication(scanBasePackages = {
        // "${yudao.info.base-package}.server",
        // "${yudao.info.base-package}.module",
        "com.xyy.saas.datasync",
        "com.xyy.saas.localserver",
}, exclude = {
        RedisAutoConfiguration.class,
        YudaoIdempotentConfiguration.class,
        YudaoRedisAutoConfiguration.class,
        // DubboAutoConfiguration.class,
        YudaoRateLimiterConfiguration.class,
        YudaoApiSignatureAutoConfiguration.class,
        // YudaoCacheAutoConfiguration.class,
        // YudaoMQAutoConfiguration.class,
        // YudaoRedisMQAutoConfiguration.class,
        // RedissonAutoConfiguration.class,
        RedissonAutoConfigurationV2.class,
        YudaoRedisMQProducerAutoConfiguration.class,
        WxMaAutoConfiguration.class,
        WxMpAutoConfiguration.class,
        MultiEnvAutoConfiguration.class,
})
@Import({TomcatServerConfiguration.class})
@EnableDataSyncScan(scanBasePackages = {
        "cn.iocoder.yudao.module.*.dal.dataobject",
        "com.xyy.saas.**.dal.dataobject.*",
        "com.xyy.saas.localserver.entity",
        // "com.xyy.saas.inquiry"
}, excludePackages = {
        "cn.iocoder.yudao.module.infra.dal.dataobject.demo",
        // com.xyy.saas.inquiry.util.RedisUtils 还未初始化spring上下文
        "com.xyy.saas.inquiry.util",
        // 云端dubbo 本地没有依赖
        "com.xyy.saas.inquiry.config",
}, baseEntity = {
        BaseDO.class
}, syncType = EnableDataSyncScan.SyncType.server)

public class CloudServerApplication {

    public static void main(String[] args) {
        System.setProperty("spring.main.allow-circular-references", "true");
        SpringApplication app = new SpringApplication(CloudServerApplication.class);
        app.setAllowCircularReferences(true);
        app.run(args);
        // new SpringApplicationBuilder(YudaoServerApplication.class)
        //         .applicationStartup(new BufferingApplicationStartup(20480))
        //         .run(args);
        //
        //
        // log.error(">>>>>applicationName [{}] 服务启动成功 Time:{} <<<<<" , property, new Date());
    }

    @Bean
    public IdentifierGenerator idGenerator(JdbcTemplate jdbcTemplate) {
        return new TenantIdentifierGenerator(jdbcTemplate);
    }

    @Bean
    public AuthorizeRequestsCustomizer drugstoreAuthorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // Swagger 接口文档
                // registry.requestMatchers("/v3/api-docs/**").permitAll()
                //         .requestMatchers("/webjars/**").permitAll()
                //         .requestMatchers("/swagger-ui.html").permitAll()
                //         .requestMatchers("/swagger-ui/**").permitAll();
                // Spring Boot Actuator 的安全配置
                registry.requestMatchers("/actuator").permitAll()
                        .requestMatchers("/actuator/**").permitAll();
                // Druid 监控
                registry.requestMatchers("/druid/**").permitAll();
                // 文件读取
                registry.requestMatchers(buildAdminApi("/infra/file/*/get/**")).permitAll();
            }

        };
    }

   /*  @Bean
    public WxMpService wxMpService(WxMpConfigStorage configStorage, WxMpProperties wxMpProperties) {
        HttpClientType httpClientType = wxMpProperties.getConfigStorage().getHttpClientType();
        WxMpService wxMpService;
        switch (httpClientType) {
            case OkHttp:
                wxMpService = newWxMpServiceOkHttpImpl();
                break;
            case JoddHttp:
                wxMpService = newWxMpServiceJoddHttpImpl();
                break;
            case HttpClient:
                wxMpService = newWxMpServiceHttpClientImpl();
                break;
            default:
                wxMpService = newWxMpServiceImpl();
                break;
        }
        if (configStorage != null && configStorage.getAppId() != null) {
            wxMpService.setWxMpConfigStorage(configStorage);
        }
        return wxMpService;
    }

    private WxMpService newWxMpServiceImpl() {
        return new WxMpServiceImpl();
    }

    private WxMpService newWxMpServiceHttpClientImpl() {
        return new WxMpServiceHttpClientImpl();
    }

    private WxMpService newWxMpServiceOkHttpImpl() {
        return new WxMpServiceOkHttpImpl();
    }

    private WxMpService newWxMpServiceJoddHttpImpl() {
        return new WxMpServiceJoddHttpImpl();
    }


    @Bean
    // @ConditionalOnBean({WxMaConfig.class})
    public WxMaService wxMaService(WxMaConfig wxMaConfig, WxMaProperties wxMaProperties) {
        com.binarywang.spring.starter.wxjava.miniapp.enums.HttpClientType httpClientType = wxMaProperties.getConfigStorage().getHttpClientType();
        Object wxMaService;
        switch (httpClientType) {
            case OkHttp:
                wxMaService = new WxMaServiceOkHttpImpl();
                break;
            case JoddHttp:
                wxMaService = new WxMaServiceJoddHttpImpl();
                break;
            case HttpClient:
                wxMaService = new WxMaServiceHttpClientImpl();
                break;
            default:
                wxMaService = new WxMaServiceImpl();
        }
        if (wxMaConfig != null && wxMaConfig.getAppid() != null) {
            ((WxMaService) wxMaService).setWxMaConfig(wxMaConfig);
        }
        return (WxMaService) wxMaService;
    } */

}
