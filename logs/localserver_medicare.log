00:18:22.516 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m17s880ms).
00:18:39.324 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:18:39.378 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0]
00:18:39.401 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0], 最大base版本:[40]
00:18:39.405 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_third_app]
00:18:39.407 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1]
00:19:09.732 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:19:09.738 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0]
00:19:09.741 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0], 最大base版本:[17]
00:19:09.743 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job]
00:19:09.744 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job], pull次数:[1]
00:19:39.963 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:19:40.005 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0]
00:19:40.043 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0], 最大base版本:[24]
00:19:40.049 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_config]
00:19:40.050 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_config], pull次数:[1]
00:20:10.436 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:20:10.534 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0]
00:20:10.597 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0], 最大base版本:[1]
00:20:10.603 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_log]
00:20:10.603 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_log], pull次数:[1]
00:20:41.327 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:20:41.475 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0]
00:20:41.533 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0], 最大base版本:[572]
00:20:41.549 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_package]
00:20:41.553 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_package], pull次数:[1]
00:21:12.034 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:21:12.063 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0]
00:21:12.076 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0], 最大base版本:[64]
00:21:12.077 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_code]
00:21:12.078 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_code], pull次数:[1]
00:21:42.268 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:21:42.272 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0]
00:21:42.274 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0], 最大base版本:[165]
00:21:42.276 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_channel]
00:21:42.278 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_channel], pull次数:[1]
00:22:12.303 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:22:12.308 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0]
00:22:12.312 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0], 最大base版本:[2]
00:22:12.316 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_qualification_info]
00:22:12.318 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1]
00:22:42.394 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:22:42.409 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0]
00:22:42.411 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0], 最大base版本:[33]
00:22:42.411 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib]
00:22:42.412 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1]
00:24:06.012 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
00:24:06.223 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:24:06.241 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0]
00:24:06.344 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
00:24:36.377 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
00:24:36.412 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30400]ms.
00:24:36.412 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
00:24:38.995 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0], 最大base版本:[455000]
00:24:39.018 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_refresh_token]
00:24:39.019 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1]
00:25:09.095 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:25:09.116 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0]
00:25:09.124 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:25:09.125 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant]
00:25:09.125 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant], pull次数:[1]
00:55:25.682 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m25s743ms).
00:55:35.162 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:55:35.295 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0]
00:55:35.406 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0], 最大base版本:[178]
00:55:35.409 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_tag]
00:55:35.615 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_tag], pull次数:[1]
00:56:06.019 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:56:06.057 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0]
00:56:06.066 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0], 最大base版本:[2]
00:56:06.071 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_category]
00:56:06.075 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_category], pull次数:[1]
00:56:36.684 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:56:36.691 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0]
00:56:36.698 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0], 最大base版本:[1]
00:56:36.701 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file_content]
00:56:36.703 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file_content], pull次数:[1]
00:57:06.803 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:57:06.841 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0]
00:57:06.855 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:57:06.857 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_access_token]
00:57:06.857 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1]
00:57:37.040 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:57:37.084 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0]
00:57:37.109 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:57:37.117 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_type]
00:57:37.119 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_type], pull次数:[1]
00:58:07.574 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:58:07.630 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0]
00:58:07.649 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0], 最大base版本:[227]
00:58:07.656 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job_log]
00:58:07.662 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job_log], pull次数:[1]
00:58:37.961 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:58:38.008 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0]
00:58:38.022 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:58:38.023 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_use_info]
00:58:38.023 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1]
00:59:08.146 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:59:08.156 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0]
00:59:08.158 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0], 最大base版本:[6]
00:59:08.159 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib_sync]
00:59:08.160 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1]
00:59:38.345 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
00:59:38.438 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0]
00:59:38.508 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0], 最大base版本:[131]
00:59:38.510 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_api_access_log]
00:59:38.514 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1]
01:00:08.838 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:00:08.896 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0]
01:00:08.909 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
01:00:08.909 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_data]
01:00:08.911 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_data], pull次数:[1]
01:00:41.309 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:00:41.350 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0]
01:00:41.368 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0], 最大base版本:[4896]
01:00:41.372 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_relation]
01:00:41.386 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1]
01:01:11.602 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:01:11.637 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0]
01:01:11.659 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0], 最大base版本:[28]
01:01:11.690 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_oa_white_list]
01:01:11.695 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1]
01:01:41.948 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:01:42.024 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0]
01:01:42.068 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0], 最大base版本:[2]
01:01:42.071 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level]
01:01:42.072 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level], pull次数:[1]
01:02:12.307 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:02:12.379 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0]
01:02:12.435 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0], 最大base版本:[3]
01:02:12.439 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_catalog]
01:02:12.441 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_catalog], pull次数:[1]
01:02:43.036 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:02:43.067 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0]
01:02:43.105 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0], 最大base版本:[84]
01:02:43.109 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_share_relation]
01:02:43.110 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1]
01:32:25.582 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=29m59s691ms).
01:32:43.213 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:32:43.249 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1], 总计条数:[0]
01:32:43.284 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
01:32:43.287 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_definition_info]
01:32:43.297 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1]
01:33:14.658 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:33:14.739 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0]
01:33:14.745 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0], 最大base版本:[224]
01:33:14.750 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_regulatory_catalog_detail]
01:33:14.762 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1]
01:34:01.885 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
01:34:02.200 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
01:34:32.433 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
01:34:32.466 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30582]ms.
01:34:32.468 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
01:34:40.699 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:34:40.711 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0]
01:34:40.747 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0], 最大base版本:[436715]
01:34:40.748 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_category]
01:34:40.749 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_category], pull次数:[1]
01:35:12.893 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:35:12.944 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0]
01:35:12.976 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0], 最大base版本:[78997]
01:35:12.983 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_user_role]
01:35:12.990 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_user_role], pull次数:[1]
01:35:43.902 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:35:43.938 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0]
01:35:43.966 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0], 最大base版本:[159]
01:35:43.968 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_finger_print]
01:35:43.969 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1]
01:36:14.194 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:36:14.203 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0]
01:36:14.205 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0], 最大base版本:[4]
01:36:14.207 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_clock_in_log]
01:36:14.207 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1]
01:36:44.268 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:36:44.306 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0]
01:36:44.319 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0], 最大base版本:[3]
01:36:44.320 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_template]
01:36:44.321 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_template], pull次数:[1]
01:37:14.405 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:37:14.412 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0]
01:37:14.423 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0], 最大base版本:[3]
01:37:14.427 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role]
01:37:14.427 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role], pull次数:[1]
01:37:44.885 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:37:44.894 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0]
01:37:44.898 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0], 最大base版本:[20]
01:37:44.900 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_sign_in_config]
01:37:44.900 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1]
01:38:14.948 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:38:14.977 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0]
01:38:15.002 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0], 最大base版本:[7]
01:38:15.006 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_form]
01:38:15.008 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_form], pull次数:[1]
01:38:45.120 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:38:45.124 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0]
01:38:45.127 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0], 最大base版本:[2]
01:38:45.128 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_quality_change_record]
01:38:45.130 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1]
01:39:15.843 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
01:39:15.868 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0]
01:39:15.896 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0], 最大base版本:[40]
01:39:15.897 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_users]
01:39:15.898 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_users], pull次数:[1]
02:09:26.261 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:09:26.283 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0]
02:09:26.324 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0], 最大base版本:[620]
02:09:26.329 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_template]
02:09:26.330 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_template], pull次数:[1]
02:09:33.598 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m7s949ms).
02:09:56.380 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:09:56.398 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0]
02:09:56.415 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0], 最大base版本:[5]
02:09:56.418 INFO  [.CycleLoopPullSubscriberExecutor:112] [] - 数据同步,全部表数据同步完成, 机构号:[1], 最后一个表名:[system_sms_template], 总同步表次数:[87]
02:09:56.442 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@29b972d2] syncPost event[com.xyy.saas.datasync.client.event.PullDataFinishedEvent]=[{}]
02:09:56.531 INFO  [ingleThreadPullSubscriberExecutor:44] [] - 数据同步,单线程拉取任务结束,释放信号量, 机构号:[1], 耗时:[2812832]ms.
02:09:56.547 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[pull-task], TenantId: [1], interval : [300]秒
02:14:10.503 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
02:14:11.109 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
02:14:41.537 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
02:14:41.605 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[31115]ms.
02:14:41.607 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
02:14:56.589 INFO  [ingleThreadPullSubscriberExecutor:40] [] - 数据同步,单线程拉取任务开始, 机构号:[1].
02:14:56.607 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@29b972d2] syncPost event[com.xyy.saas.datasync.client.event.PullDataStartedEvent]=[{}]
02:14:56.677 INFO  [.CycleLoopPullSubscriberExecutor:107] [] - 数据同步,开始从云端pull数据,待同步表数量:[87], 机构号:[1]
02:14:56.678 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_instance_copy]
02:14:56.678 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1]
02:15:27.542 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:15:27.593 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0]
02:15:27.622 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0], 最大base版本:[0]
02:15:27.626 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level_record]
02:15:27.627 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level_record], pull次数:[1]
02:15:57.984 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:15:58.030 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0]
02:15:58.036 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0], 最大base版本:[26]
02:15:58.038 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_data_source_config]
02:15:58.040 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1]
02:46:17.091 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:46:17.171 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0]
02:46:17.236 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
02:46:17.245 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_codegen_table]
02:46:17.249 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1]
02:46:22.659 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m18s903ms).
02:46:47.814 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:46:47.835 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0]
02:46:47.849 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0], 最大base版本:[169]
02:46:47.852 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_account]
02:46:47.855 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_account], pull次数:[1]
02:47:17.913 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:47:17.926 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0]
02:47:17.931 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0], 最大base版本:[4]
02:47:17.932 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_point_record]
02:47:17.936 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_point_record], pull次数:[1]
02:47:48.050 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:47:48.105 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0]
02:47:48.114 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0], 最大base版本:[77]
02:47:48.116 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_transfer_record]
02:47:48.119 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1]
02:48:18.315 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:48:18.399 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0]
02:48:18.465 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0], 最大base版本:[6]
02:48:18.470 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_relation]
02:48:18.470 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1]
02:48:48.743 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:48:48.755 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0]
02:48:48.760 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
02:48:48.760 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_operate_log]
02:48:48.762 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_operate_log], pull次数:[1]
02:49:21.188 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:49:21.228 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0]
02:49:21.241 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0], 最大base版本:[8114]
02:49:21.242 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role_menu]
02:49:21.245 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role_menu], pull次数:[1]
02:49:51.780 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:49:51.788 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0]
02:49:51.796 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0], 最大base版本:[3935]
02:49:51.799 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_user_group]
02:49:51.800 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_user_group], pull次数:[1]
02:50:21.859 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:50:21.866 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0]
02:50:21.867 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0], 最大base版本:[1]
02:50:21.868 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_listener]
02:50:21.868 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1]
02:50:52.000 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:50:52.045 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0]
02:50:52.070 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0], 最大base版本:[0]
02:50:52.071 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_user]
02:50:52.073 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_user], pull次数:[1]
02:51:22.276 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:51:22.279 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0]
02:51:22.281 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0], 最大base版本:[0]
02:51:22.281 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_price_adjustment_record]
02:51:22.282 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1]
02:51:52.337 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:51:52.374 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0]
02:51:52.397 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0], 最大base版本:[7]
02:51:52.404 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_menu]
02:51:52.405 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_menu], pull次数:[1]
02:52:22.989 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
02:52:23.066 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0]
02:52:23.099 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0], 最大base版本:[1419]
02:52:23.106 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_bpm_business_relation]
02:52:23.110 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1]
03:22:46.435 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m23s726ms).
03:22:46.959 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:22:46.984 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1], 总计条数:[0]
03:22:46.988 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1], 总计条数:[0], 最大base版本:[7]
03:22:46.992 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_approve]
03:22:46.996 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1]
03:23:17.055 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:23:17.071 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1], 总计条数:[0]
03:23:17.076 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1], 总计条数:[0], 最大base版本:[0]
03:23:17.077 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_expression]
03:23:17.081 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1]
03:23:47.168 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:23:47.185 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1], 总计条数:[0]
03:23:47.191 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1], 总计条数:[0], 最大base版本:[2]
03:23:47.198 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_codegen_column]
03:23:47.204 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1]
03:24:18.649 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:24:18.675 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1], 总计条数:[0]
03:24:18.732 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1], 总计条数:[0], 最大base版本:[3468]
03:24:18.735 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_experience_record]
03:24:18.742 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_experience_record], pull次数:[1]
03:24:24.233 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
03:24:24.626 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
03:24:49.044 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:24:49.094 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_experience_record], pull次数:[1], 总计条数:[0]
03:24:49.143 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_experience_record], pull次数:[1], 总计条数:[0], 最大base版本:[51]
03:24:49.163 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_login_log]
03:24:49.170 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_login_log], pull次数:[1]
03:24:54.678 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
03:24:54.696 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30464]ms.
03:24:54.703 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
03:25:20.535 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:25:20.603 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_login_log], pull次数:[1], 总计条数:[0]
03:25:20.631 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_login_log], pull次数:[1], 总计条数:[0], 最大base版本:[1292]
03:25:20.634 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_certificate]
03:25:20.641 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1]
03:25:50.919 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:25:50.954 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1], 总计条数:[0]
03:25:50.977 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1], 总计条数:[0], 最大base版本:[3]
03:25:50.980 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_address]
03:25:50.988 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_address], pull次数:[1]
03:26:21.197 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:26:21.245 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_address], pull次数:[1], 总计条数:[0]
03:26:21.283 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_address], pull次数:[1], 总计条数:[0], 最大base版本:[8]
03:26:21.299 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_user_post]
03:26:21.300 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_user_post], pull次数:[1]
03:26:51.726 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:26:51.769 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_user_post], pull次数:[1], 总计条数:[0]
03:26:51.793 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_user_post], pull次数:[1], 总计条数:[0], 最大base版本:[1]
03:26:51.798 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_group]
03:26:51.801 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_group], pull次数:[1]
03:27:22.025 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:27:22.058 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_group], pull次数:[1], 总计条数:[0]
03:27:22.065 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_group], pull次数:[1], 总计条数:[0], 最大base版本:[1]
03:27:22.065 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_client]
03:27:22.067 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_client], pull次数:[1]
03:27:52.215 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:27:52.327 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_social_client], pull次数:[1], 总计条数:[0]
03:27:52.356 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_client], pull次数:[1], 总计条数:[0], 最大base版本:[3]
03:27:52.359 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dept]
03:27:52.361 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dept], pull次数:[1]
03:28:22.627 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:28:22.703 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_dept], pull次数:[1], 总计条数:[0]
03:28:22.752 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dept], pull次数:[1], 总计条数:[0], 最大base版本:[2]
03:28:22.753 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_code]
03:28:22.764 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1]
03:28:53.090 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:28:53.114 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1], 总计条数:[0]
03:28:53.129 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1], 总计条数:[0], 最大base版本:[0]
03:28:53.132 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_price_adjustment_detail]
03:28:53.132 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1]
03:29:23.409 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:29:23.435 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1], 总计条数:[0]
03:29:23.457 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1], 总计条数:[0], 最大base版本:[7]
03:29:23.461 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_user]
03:29:23.461 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_user], pull次数:[1]
03:59:37.162 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m20s518ms).
03:59:44.198 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
03:59:44.258 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_user], pull次数:[1], 总计条数:[0]
03:59:44.326 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_user], pull次数:[1], 总计条数:[0], 最大base版本:[26]
03:59:44.335 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_info]
03:59:44.343 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_info], pull次数:[1]
04:00:14.989 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:00:15.020 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_info], pull次数:[1], 总计条数:[0]
04:00:15.035 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_info], pull次数:[1], 总计条数:[0], 最大base版本:[19]
04:00:15.044 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_transmission_service_pack_relation]
04:00:15.060 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1]
04:00:45.158 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:00:45.184 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1], 总计条数:[0]
04:00:45.192 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
04:00:45.194 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_user_bind]
04:00:45.195 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1]
04:01:15.381 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:01:15.514 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1], 总计条数:[0]
04:01:15.558 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1], 总计条数:[0], 最大base版本:[0]
04:01:15.560 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_oa_leave]
04:01:15.564 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1]
04:01:45.817 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:01:45.830 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1], 总计条数:[0]
04:01:45.831 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1], 总计条数:[0], 最大base版本:[10]
04:01:45.835 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file]
04:01:45.835 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file], pull次数:[1]
04:02:22.079 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:02:22.129 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_file], pull次数:[1], 总计条数:[0]
04:02:22.154 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file], pull次数:[1], 总计条数:[0], 最大base版本:[30282]
04:02:22.170 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_log]
04:02:22.171 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_log], pull次数:[1]
04:02:52.243 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:02:52.256 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_mail_log], pull次数:[1], 总计条数:[0]
04:02:52.285 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
04:02:52.286 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_app_version]
04:02:52.286 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_app_version], pull次数:[1]
04:03:22.485 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:03:22.490 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_app_version], pull次数:[1], 总计条数:[0]
04:03:22.492 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_app_version], pull次数:[1], 总计条数:[0], 最大base版本:[19]
04:03:22.493 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_quality_change_detail]
04:03:22.493 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1]
04:03:52.885 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:03:52.915 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1], 总计条数:[0]
04:03:52.930 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1], 总计条数:[0], 最大base版本:[40]
04:03:52.934 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_api_error_log]
04:03:52.935 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1]
04:04:23.189 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:04:23.195 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1], 总计条数:[0]
04:04:23.198 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
04:04:23.198 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_app_version_detail]
04:04:23.201 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1]
04:04:45.218 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
04:04:45.449 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
04:04:53.263 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:04:53.273 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1], 总计条数:[0]
04:04:53.277 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
04:04:53.277 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_client]
04:04:53.278 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1]
04:05:15.545 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
04:05:15.579 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30360]ms.
04:05:15.579 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
04:05:24.372 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:05:24.379 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1], 总计条数:[0]
04:05:24.385 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1], 总计条数:[0], 最大base版本:[140]
04:05:24.390 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notify_message]
04:05:24.391 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notify_message], pull次数:[1]
04:35:37.767 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:35:37.962 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_notify_message], pull次数:[1], 总计条数:[0]
04:35:37.982 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notify_message], pull次数:[1], 总计条数:[0], 最大base版本:[9]
04:35:37.984 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_sign_in_record]
04:35:37.984 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1]
04:35:49.613 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m12s303ms).
04:36:08.131 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:36:08.222 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1], 总计条数:[0]
04:36:08.227 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1], 总计条数:[0], 最大base版本:[6]
04:36:08.229 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notice]
04:36:08.230 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notice], pull次数:[1]
04:36:38.327 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:36:38.362 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_notice], pull次数:[1], 总计条数:[0]
04:36:38.381 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notice], pull次数:[1], 总计条数:[0], 最大base版本:[2]
04:36:38.391 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file_config]
04:36:38.400 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file_config], pull次数:[1]
04:37:08.483 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:37:08.512 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_file_config], pull次数:[1], 总计条数:[0]
04:37:08.519 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file_config], pull次数:[1], 总计条数:[0], 最大base版本:[3]
04:37:08.520 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notify_template]
04:37:08.521 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notify_template], pull次数:[1]
04:37:38.868 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:37:38.960 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_notify_template], pull次数:[1], 总计条数:[0]
04:37:38.987 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notify_template], pull次数:[1], 总计条数:[0], 最大base版本:[120]
04:37:38.989 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_post]
04:37:38.990 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_post], pull次数:[1]
04:38:09.505 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:38:09.530 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_post], pull次数:[1], 总计条数:[0]
04:38:09.631 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_post], pull次数:[1], 总计条数:[0], 最大base版本:[4]
04:38:09.640 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_config]
04:38:09.640 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_config], pull次数:[1]
04:38:39.875 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:38:39.920 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0]
04:38:39.992 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0], 最大base版本:[40]
04:38:39.999 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_third_app]
04:38:39.999 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1]
04:39:13.745 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:39:13.940 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0]
04:39:13.997 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0], 最大base版本:[17]
04:39:14.006 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job]
04:39:14.007 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job], pull次数:[1]
04:39:44.346 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:39:44.387 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0]
04:39:44.397 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0], 最大base版本:[24]
04:39:44.410 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_config]
04:39:44.411 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_config], pull次数:[1]
04:40:14.661 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:40:14.730 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0]
04:40:14.758 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0], 最大base版本:[1]
04:40:14.764 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_log]
04:40:14.770 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_log], pull次数:[1]
04:40:45.903 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:40:45.956 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0]
04:40:45.976 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0], 最大base版本:[572]
04:40:45.978 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_package]
04:40:45.979 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_package], pull次数:[1]
04:41:16.347 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:41:16.397 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0]
04:41:16.455 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0], 最大base版本:[64]
04:41:16.469 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_code]
04:41:16.474 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_code], pull次数:[1]
04:41:46.890 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
04:41:46.974 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0]
04:41:46.997 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0], 最大base版本:[165]
04:41:47.002 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_channel]
04:41:47.014 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_channel], pull次数:[1]
05:12:12.335 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
05:12:12.447 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0]
05:12:12.499 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0], 最大base版本:[2]
05:12:12.511 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_qualification_info]
05:12:12.516 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1]
05:12:13.351 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m23s591ms).
05:12:44.094 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
05:12:44.106 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0]
05:12:44.109 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0], 最大base版本:[33]
05:12:44.113 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib]
05:12:44.114 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1]
05:28:13.384 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=14m59s714ms).
05:29:21.187 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
05:29:21.665 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
05:45:50.172 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
05:45:50.204 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0]
05:45:50.199 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
05:45:50.249 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30679]ms.
05:45:50.249 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
05:45:50.291 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0], 最大base版本:[455000]
05:45:50.297 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_refresh_token]
05:45:50.297 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1]
05:46:11.858 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m28s380ms).
05:46:20.440 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
05:46:20.446 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0]
05:46:20.449 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
05:46:20.450 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant]
05:46:20.450 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant], pull次数:[1]
05:46:50.749 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
05:46:50.751 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0]
05:46:50.753 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0], 最大base版本:[178]
05:46:50.754 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_tag]
05:46:50.755 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_tag], pull次数:[1]
07:00:03.049 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h13m21s154ms).
07:00:12.081 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:00:12.130 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0]
07:00:12.310 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0], 最大base版本:[2]
07:00:12.312 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_category]
07:00:12.313 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_category], pull次数:[1]
07:00:44.166 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:00:44.171 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0]
07:00:44.186 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0], 最大base版本:[1]
07:00:44.190 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file_content]
07:00:44.190 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file_content], pull次数:[1]
07:01:14.296 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:01:14.312 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0]
07:01:14.324 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0], 最大base版本:[0]
07:01:14.326 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_access_token]
07:01:14.326 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1]
07:09:09.649 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m6s516ms).
07:09:21.319 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:09:21.388 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0]
07:09:21.711 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
07:09:21.724 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_type]
07:09:21.727 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_type], pull次数:[1]
07:09:56.124 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:09:56.456 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0]
07:09:56.812 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0], 最大base版本:[227]
07:09:56.854 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job_log]
07:09:56.858 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job_log], pull次数:[1]
07:10:29.797 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:10:29.846 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0]
07:10:29.909 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
07:10:29.922 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_use_info]
07:10:29.922 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1]
07:11:00.485 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:11:00.538 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0]
07:11:00.597 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0], 最大base版本:[6]
07:11:00.600 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib_sync]
07:11:00.602 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1]
07:11:31.641 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:11:31.714 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0]
07:11:31.798 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0], 最大base版本:[131]
07:11:31.805 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_api_access_log]
07:11:31.805 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1]
07:12:02.471 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:12:02.593 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0]
07:12:02.639 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
07:12:02.646 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_data]
07:12:02.649 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_data], pull次数:[1]
07:12:34.627 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:12:34.637 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0]
07:12:34.642 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0], 最大base版本:[4896]
07:12:34.645 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_relation]
07:12:34.645 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1]
07:13:04.735 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:13:04.762 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0]
07:13:04.791 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0], 最大base版本:[28]
07:13:04.792 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_oa_white_list]
07:13:04.793 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1]
07:13:34.930 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:13:34.932 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0]
07:13:34.933 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0], 最大base版本:[2]
07:13:34.934 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level]
07:13:34.935 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level], pull次数:[1]
07:14:04.977 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:14:05.003 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0]
07:14:05.014 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0], 最大base版本:[3]
07:14:05.015 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_catalog]
07:14:05.016 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_catalog], pull次数:[1]
07:14:37.361 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:14:37.405 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0]
07:14:37.418 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0], 最大base版本:[84]
07:14:37.426 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_share_relation]
07:14:37.426 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1]
07:15:07.542 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:15:07.556 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1], 总计条数:[0]
07:15:07.661 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_share_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
07:15:07.666 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_definition_info]
07:15:07.668 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1]
07:15:38.074 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:15:38.093 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0]
07:15:38.123 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0], 最大base版本:[224]
07:15:38.127 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_regulatory_catalog_detail]
07:15:38.127 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1]
07:16:17.989 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
07:16:18.315 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
07:16:26.371 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:16:26.389 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0]
07:16:26.444 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0], 最大base版本:[436715]
07:16:26.447 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_category]
07:16:26.447 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_category], pull次数:[1]
07:16:48.364 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
07:16:48.371 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[30380]ms.
07:16:48.371 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
07:16:59.677 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:16:59.691 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0]
07:16:59.716 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0], 最大base版本:[78997]
07:16:59.717 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_user_role]
07:16:59.717 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_user_role], pull次数:[1]
07:47:00.013 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m20s67ms).
07:47:19.879 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:47:19.901 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0]
07:47:19.991 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0], 最大base版本:[159]
07:47:19.995 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_finger_print]
07:47:19.996 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1]
07:47:50.547 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:47:50.553 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0]
07:47:50.555 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0], 最大base版本:[4]
07:47:50.556 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_clock_in_log]
07:47:50.560 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1]
07:48:20.613 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:48:20.615 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0]
07:48:20.616 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0], 最大base版本:[3]
07:48:20.616 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_template]
07:48:20.618 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_template], pull次数:[1]
07:48:50.758 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:48:50.830 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0]
07:48:50.904 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0], 最大base版本:[3]
07:48:50.908 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role]
07:48:50.908 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role], pull次数:[1]
07:49:21.958 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:49:22.228 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0]
07:49:22.379 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0], 最大base版本:[20]
07:49:22.384 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_sign_in_config]
07:49:22.426 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1]
07:49:53.648 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:49:53.953 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0]
07:49:54.124 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0], 最大base版本:[7]
07:49:54.169 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_form]
07:49:54.171 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_form], pull次数:[1]
07:50:24.575 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:50:24.626 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0]
07:50:24.649 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0], 最大base版本:[2]
07:50:24.653 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_quality_change_record]
07:50:24.654 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1]
07:50:55.440 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:50:55.505 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0]
07:50:55.530 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0], 最大base版本:[40]
07:50:55.531 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_users]
07:50:55.531 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_users], pull次数:[1]
07:51:26.857 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:51:26.921 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0]
07:51:26.974 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0], 最大base版本:[620]
07:51:26.976 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_template]
07:51:26.976 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_template], pull次数:[1]
07:51:57.276 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
07:51:57.350 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0]
07:51:57.365 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0], 最大base版本:[5]
07:51:57.366 INFO  [.CycleLoopPullSubscriberExecutor:112] [] - 数据同步,全部表数据同步完成, 机构号:[1], 最后一个表名:[system_sms_template], 总同步表次数:[87]
07:51:57.399 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@29b972d2] syncPost event[com.xyy.saas.datasync.client.event.PullDataFinishedEvent]=[{}]
07:51:57.478 INFO  [ingleThreadPullSubscriberExecutor:44] [] - 数据同步,单线程拉取任务结束,释放信号量, 机构号:[1], 耗时:[2826117]ms.
07:51:57.503 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[pull-task], TenantId: [1], interval : [300]秒
08:16:19.000 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=21m18s718ms).
09:10:04.366 INFO  [ush.SingleThreadPublisherExecutor:43] [] - 数据同步,单线程推送全量任务开始, 机构号:[1].
09:10:05.447 INFO  [rker.push.CyclePublisherExecutor:100] [] - 数据同步 push开始: 表名 system_login_log 条数 7
09:10:23.404 INFO  [ingleThreadPullSubscriberExecutor:40] [] - 数据同步,单线程拉取任务开始, 机构号:[1].
09:10:23.430 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@29b972d2] syncPost event[com.xyy.saas.datasync.client.event.PullDataStartedEvent]=[{}]
09:10:23.476 INFO  [.CycleLoopPullSubscriberExecutor:107] [] - 数据同步,开始从云端pull数据,待同步表数量:[87], 机构号:[1]
09:10:23.479 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_instance_copy]
09:10:23.481 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1]
09:10:26.309 WARN  [om.zaxxer.hikari.pool.HikariPool:797] [] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53m7s158ms).
09:10:35.855 ERROR [orker.push.CyclePublisherExecutor:54] [] - 数据同步 pushInvoker error: Timeout on blocking read for *********** NANOSECONDS
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.push(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataPublisherImpl.push(DataPublisherImpl.java:30)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:101)
	at com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor.pushInvoker(CyclePublisherExecutor.java:51)
	at com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor$1.run(SingleThreadPublisherExecutor.java:44)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 18 common frames omitted
09:10:35.895 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[push-task], TenantId: [1], interval : [600]秒
09:10:35.895 INFO  [ush.SingleThreadPublisherExecutor:47] [] - 数据同步,单线程推送全量任务结束,释放信号量, 机构号:[1], 耗时:[31551]ms.
09:10:53.709 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:10:53.720 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0]
09:10:53.728 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0], 最大base版本:[0]
09:10:53.729 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level_record]
09:10:53.741 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level_record], pull次数:[1]
09:11:24.071 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:11:24.127 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0]
09:11:24.187 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0], 最大base版本:[26]
09:11:24.190 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_data_source_config]
09:11:24.191 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1]
09:11:54.349 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:11:54.353 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0]
09:11:54.357 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
09:11:54.359 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_codegen_table]
09:11:54.359 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1]
09:12:24.665 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:12:24.676 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0]
09:12:24.720 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0], 最大base版本:[169]
09:12:24.721 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_account]
09:12:24.723 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_account], pull次数:[1]
09:12:54.772 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:12:54.802 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0]
09:12:54.811 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0], 最大base版本:[4]
09:12:54.812 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_point_record]
09:12:54.816 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_point_record], pull次数:[1]
09:13:25.051 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:13:25.072 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0]
09:13:25.080 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0], 最大base版本:[77]
09:13:25.084 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_transfer_record]
09:13:25.085 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1]
09:13:55.193 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:13:55.212 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0]
09:13:55.223 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0], 最大base版本:[6]
09:13:55.239 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_relation]
09:13:55.242 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1]
09:14:25.379 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:14:25.402 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0]
09:14:25.410 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
09:14:25.410 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_operate_log]
09:14:25.411 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_operate_log], pull次数:[1]
09:14:56.519 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:14:56.539 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0]
09:14:56.544 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0], 最大base版本:[8114]
09:14:56.544 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role_menu]
09:14:56.545 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role_menu], pull次数:[1]
09:15:26.629 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:15:26.631 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0]
09:15:26.632 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0], 最大base版本:[3935]
09:15:26.633 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_user_group]
09:15:26.633 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_user_group], pull次数:[1]
09:15:56.705 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:15:56.718 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0]
09:15:56.740 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0], 最大base版本:[1]
09:15:56.744 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_listener]
09:15:56.744 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1]
09:16:26.801 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:16:26.805 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0]
09:16:26.806 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0], 最大base版本:[0]
09:16:26.806 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_user]
09:16:26.808 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_user], pull次数:[1]
09:16:56.869 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:16:56.903 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0]
09:16:56.916 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0], 最大base版本:[0]
09:16:56.918 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_price_adjustment_record]
09:16:56.921 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1]
09:17:27.065 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:17:27.073 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0]
09:17:27.077 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0], 最大base版本:[7]
09:17:27.079 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_menu]
09:17:27.080 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_menu], pull次数:[1]
09:17:57.711 ERROR [lient.protocol.DataSubscriberImpl:53] [] - --------------pull data from cloud error:Timeout on blocking read for *********** NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for *********** NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy146.pull(Unknown Source)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:51)
	at com.xyy.saas.datasync.client.protocol.DataSubscriberImpl.pull(DataSubscriberImpl.java:36)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:67)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.cycleLoopPullInvoker(CycleLoopPullSubscriberExecutor.java:110)
	at com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor.pullInvoker(CycleLoopPullSubscriberExecutor.java:52)
	at com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor.lambda$pullInvoker$1(SingleThreadPullSubscriberExecutor.java:41)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for *********** NANOSECONDS
	... 20 common frames omitted
09:17:57.738 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0]
09:17:57.746 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0], 最大base版本:[1419]
09:17:57.765 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_bpm_business_relation]
09:17:57.765 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1]
