package com.xyy.saas.inquiry.pharmacist.api.audit;

import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/26 11:49
 */
public interface InquiryPrescriptionAuditApi {

    /**
     * 远程审方推入审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionPushAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 远程审方移除审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionRemoveAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 批量 远程审方移除审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto);
}
