package com.xyy.saas.inquiry.localmock.redis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface LocalKvStoreMapper extends BaseMapper<LocalKvStorePO> {
    
    /**
     * 根据前缀统计数量
     */
    @Select("SELECT COUNT(*) FROM local_mock_kv_store WHERE `key` LIKE CONCAT(#{prefix}, '%')")
    int countByPrefix(String prefix);
    
    /**
     * 根据前缀删除记录
     */
    @Delete("DELETE FROM local_mock_kv_store WHERE `key` LIKE CONCAT(#{prefix}, '%')")
    int deleteByPrefix(String prefix);
    
    /**
     * 根据前缀查询所有键
     */
    @Select("SELECT `key` FROM local_mock_kv_store WHERE `key` LIKE CONCAT(#{prefix}, '%')")
    List<String> selectKeysByPrefix(String prefix);
    
    /**
     * 根据前缀查询所有记录
     */
    @Select("SELECT * FROM local_mock_kv_store WHERE `key` LIKE CONCAT(#{prefix}, '%')")
    List<LocalKvStorePO> selectByPrefix(String prefix);
    

}
