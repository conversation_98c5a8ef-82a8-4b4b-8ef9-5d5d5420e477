package com.xyy.saas.inquiry.config.mq;

import com.xyy.saas.inquiry.config.mq.local.LocalDefaultLitePullConsumer;
import com.xyy.saas.inquiry.config.mq.local.LocalDefaultMQProducer;
import com.xyy.saas.inquiry.config.mq.local.LocalRocketMQTemplate;
import com.xyy.saas.inquiry.localmock.mq.LocalMqDbConsumer;
import com.xyy.saas.inquiry.localmock.mq.LocalMqDbProducer;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultLitePullConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQMessageConverter;
import org.springframework.boot.autoconfigure.condition.AnyNestedCondition;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 本地模式 RocketMQ 自动配置类
 * 参考官方 RocketMQAutoConfiguration 实现模式和结构
 *
 * <p>设计特点：</p>
 * <ul>
 *   <li>完整的条件注解体系，确保在合适的环境下生效</li>
 *   <li>参考官方自动配置类的 Bean 定义方式和条件注解使用</li>
 *   <li>与项目 Local* 模式保持一致的设计风格</li>
 *   <li>向后兼容，无需修改现有业务代码</li>
 *   <li>DefaultMQProducer 和 DefaultLitePullConsumer 不连接真实 MQ 服务器</li>
 *   <li>所有消息操作路由到本地 LocalRocketMQTemplate 实现</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Configuration
@Profile("local")
public class LocalRocketMQConfig implements ApplicationContextAware {

    // Bean 名称常量，与官方保持一致
    public static final String PRODUCER_BEAN_NAME = "defaultMQProducer";
    public static final String CONSUMER_BEAN_NAME = "defaultLitePullConsumer";
    public static final String ROCKETMQ_TEMPLATE_DEFAULT_GLOBAL_NAME = "rocketMQTemplate";

    private ApplicationContext applicationContext;

    public LocalRocketMQConfig() {
        log.info("【本地模式】LocalRocketMQConfig 本地 RocketMQ 配置初始化完成！");
    }

    @Override
    public void setApplicationContext(@Nonnull ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 本地模式下的 LocalMqDbProducer 实现
     * 负责消息的本地存储和处理
     *
     * @return LocalMqDbProducer 实例
     */
    @Bean
    @ConditionalOnMissingBean(LocalMqDbProducer.class)
    @ConditionalOnProperty(
        prefix = "saas.inquiry.local.rocketmq",
        name = "producer.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public LocalMqDbProducer localMqDbProducer() {
        log.info("【本地模式】LocalMqDbProducer 创建完成");
        return new LocalMqDbProducer();
    }

    /**
     * 本地模式下的 DefaultMQProducer 实现
     * 参考官方 RocketMQAutoConfiguration.defaultMQProducer() 方法
     * 将所有操作桥接到 LocalMqDbProducer 的本地实现
     *
     * @param localMqDbProducer 本地生产者实现
     * @return DefaultMQProducer 实例（本地模式）
     */
    @Primary
    @Bean(name = PRODUCER_BEAN_NAME)
    @ConditionalOnProperty(
        prefix = "saas.inquiry.local.rocketmq",
        name = "producer.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public DefaultMQProducer defaultMQProducer(LocalMqDbProducer localMqDbProducer) {
        // 创建本地模式的生产者包装类
        LocalDefaultMQProducer producer = new LocalDefaultMQProducer("local_producer_group");

        // 设置虚拟的 NameServer 地址，不会真实连接
        producer.setNamesrvAddr("local://127.0.0.1:9876");

        // 设置其他默认参数
        producer.setSendMsgTimeout(3000);
        producer.setRetryTimesWhenSendFailed(2);
        producer.setRetryTimesWhenSendAsyncFailed(2);
        producer.setMaxMessageSize(4 * 1024 * 1024); // 4MB

        // 桥接到本地实现
        producer.setLocalMqDbProducer(localMqDbProducer);

        log.info("【本地模式】DefaultMQProducer 创建完成，组名: {}，已桥接到 LocalMqDbProducer", producer.getProducerGroup());

        // 注意：不调用 producer.start()，避免连接真实的 MQ 服务器
        return producer;
    }

    /**
     * 本地模式下的 DefaultLitePullConsumer 实现
     * 参考官方 RocketMQAutoConfiguration 的消费者创建模式
     * 将所有操作桥接到 LocalMqDbConsumer 的本地实现
     *
     * @param localMqDbConsumer 本地消费者实现
     * @return DefaultLitePullConsumer 实例（本地模式）
     */
    @Primary
    @Bean(name = CONSUMER_BEAN_NAME)
    @ConditionalOnProperty(
        prefix = "saas.inquiry.local.rocketmq",
        name = "consumer.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public DefaultLitePullConsumer defaultLitePullConsumer(LocalMqDbConsumer localMqDbConsumer) {
        // 创建本地模式的消费者包装类
        LocalDefaultLitePullConsumer consumer = new LocalDefaultLitePullConsumer("local_consumer_group");

        // 设置虚拟的 NameServer 地址，不会真实连接
        consumer.setNamesrvAddr("local://127.0.0.1:9876");

        // 设置其他默认参数
        consumer.setPullBatchSize(10);

        // 桥接到本地实现
        consumer.setLocalMqDbConsumer(localMqDbConsumer);

        log.info("【本地模式】DefaultLitePullConsumer 创建完成，组名: {}，已桥接到 LocalMqDbConsumer", consumer.getConsumerGroup());

        // 注意：不调用 consumer.start()，避免连接真实的 MQ 服务器
        return consumer;
    }

    /**
     * 本地模式下的 RocketMQTemplate 实现
     * 参考官方 RocketMQAutoConfiguration.rocketMQTemplate() 方法
     * 使用本地 LocalRocketMQTemplate 替代官方实现
     *
     * @param rocketMQMessageConverter 消息转换器
     * @return LocalRocketMQTemplate 实例
     */
    @Primary
    @Bean(name = ROCKETMQ_TEMPLATE_DEFAULT_GLOBAL_NAME, destroyMethod = "destroy")
    @Conditional(ProducerOrConsumerPropertyCondition.class)
    public RocketMQTemplate rocketMQTemplate(RocketMQMessageConverter rocketMQMessageConverter) {
        LocalRocketMQTemplate rocketMQTemplate = new LocalRocketMQTemplate();

        // 设置生产者和消费者（虽然不会真实使用，但保持与官方一致的结构）
        if (applicationContext.containsBean(PRODUCER_BEAN_NAME)) {
            rocketMQTemplate.setProducer((DefaultMQProducer) applicationContext.getBean(PRODUCER_BEAN_NAME));
        }
        if (applicationContext.containsBean(CONSUMER_BEAN_NAME)) {
            rocketMQTemplate.setConsumer((DefaultLitePullConsumer) applicationContext.getBean(CONSUMER_BEAN_NAME));
        }

        // 设置消息转换器
        rocketMQTemplate.setMessageConverter(rocketMQMessageConverter.getMessageConverter());

        log.info("【本地模式】LocalRocketMQTemplate 创建完成，已设置生产者和消费者");
        return rocketMQTemplate;
    }

    /**
     * 本地消息数据库消费者
     * 负责从数据库中轮询消息并分发给相应的监听器
     *
     * @return LocalMqDbConsumer 实例
     */
    @Bean
    @ConditionalOnMissingBean(LocalMqDbConsumer.class)
    @ConditionalOnProperty(
        prefix = "saas.inquiry.local.rocketmq",
        name = "db-consumer.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public LocalMqDbConsumer localMqDbConsumer() {
        log.info("【本地模式】LocalMqDbConsumer 数据库消息消费者创建完成");
        return new LocalMqDbConsumer();
    }

    /**
     * 生产者或消费者属性条件类
     * 参考官方 RocketMQAutoConfiguration.ProducerOrConsumerPropertyCondition
     * 确保至少有一个生产者或消费者存在时才创建 RocketMQTemplate
     */
    static class ProducerOrConsumerPropertyCondition extends AnyNestedCondition {

        public ProducerOrConsumerPropertyCondition() {
            super(ConfigurationPhase.REGISTER_BEAN);
        }

        @ConditionalOnBean(DefaultMQProducer.class)
        static class DefaultMQProducerExistsCondition {
        }

        @ConditionalOnBean(DefaultLitePullConsumer.class)
        static class DefaultLitePullConsumerExistsCondition {
        }
    }
}
