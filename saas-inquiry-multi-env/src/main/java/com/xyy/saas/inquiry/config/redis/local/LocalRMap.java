package com.xyy.saas.inquiry.config.redis.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStorePO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.DeletedObjectListener;
import org.redisson.api.ExpiredObjectListener;
import org.redisson.api.ObjectListener;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RFuture;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.listener.MapPutListener;
import org.redisson.api.listener.MapRemoveListener;
import org.redisson.api.listener.TrackingListener;
import org.redisson.api.map.MapLoader;
import org.redisson.api.map.MapWriter;
import org.redisson.api.mapreduce.RMapReduce;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.DoubleCodec;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.misc.CompletableFutureWrapper;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 本地实现的 RMap
 * 使用 H2 数据库存储数据，支持过期时间
 */
@Slf4j
public class LocalRMap<K, V> implements RMap<K, V> {

    private final String name;
    private final LocalKvStoreMapper localKvStoreMapper;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, Object> memoryCache = new ConcurrentHashMap<>();

    public LocalRMap(String name, LocalKvStoreMapper localKvStoreMapper) {
        this.name = name;
        this.localKvStoreMapper = localKvStoreMapper;
    }

    private String getMapKey(Object key) {
        return name + ":" + key.toString();
    }

    @Override
    public V put(K key, V value) {
        V previousValue = get(key);
        putValue(key, value);
        return previousValue;
    }

    @Override
    public V putIfAbsent(K key, V value) {
        V existingValue = get(key);
        if (existingValue == null) {
            putValue(key, value);
            return null;
        }
        return existingValue;
    }

    /**
     * Stores the specified <code>value</code> mapped by <code>key</code>
     * only if mapping already exists.
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>null</code> if key doesn't exist in the hash and value hasn't been set.
     * Previous value if key already exists in the hash and new value has been stored.
     */
    @Override
    public V putIfExists(K key, V value) {
        V existingValue = get(key);
        if (existingValue != null) {
            putValue(key, value);
            return existingValue;
        }
        return null;
    }

    /**
     * Returns random keys from this map limited by <code>count</code>
     *
     * @param count - keys amount to return
     * @return random keys
     */
    @Override
    public Set<K> randomKeys(int count) {
        return Set.of();
    }

    /**
     * Returns random map entries from this map limited by <code>count</code>
     *
     * @param count - entries amount to return
     * @return random entries
     */
    @Override
    public Map<K, V> randomEntries(int count) {
        return Map.of();
    }

    /**
     * Loads all map entries to this Redis map using {@link MapLoader}.
     *
     * @param replaceExistingValues - <code>true</code> if existed values should be replaced, <code>false</code> otherwise.
     * @param parallelism           - parallelism level, used to increase speed of process execution
     */
    @Override
    public void loadAll(boolean replaceExistingValues, int parallelism) {

    }

    /**
     * Loads map entries using {@link MapLoader} whose keys are listed in defined <code>keys</code> parameter.
     *
     * @param keys                  - map keys
     * @param replaceExistingValues - <code>true</code> if existed values should be replaced, <code>false</code> otherwise.
     * @param parallelism           - parallelism level, used to increase speed of process execution
     */
    @Override
    public void loadAll(Set<? extends K> keys, boolean replaceExistingValues, int parallelism) {

    }

    @Override
    public V get(Object key) {
        String mapKey = getMapKey(key);
        
        // 先检查内存缓存
        Object cached = memoryCache.get(mapKey);
        if (cached != null) {
            return (V) cached;
        }

        // 从数据库查询
        LocalKvStorePO po = localKvStoreMapper.selectById(mapKey);
        if (po == null) {
            return null;
        }

        // 检查是否过期
        if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
            localKvStoreMapper.deleteById(mapKey);
            return null;
        }

        try {
            V value = (V) objectMapper.readValue(po.getValue(), Object.class);
            memoryCache.put(mapKey, value);
            return value;
        } catch (JsonProcessingException e) {
            log.error("Error deserializing value for key {}: {}", mapKey, e.getMessage());
            return null;
        }
    }

    @Override
    public V remove(Object key) {
        V previousValue = get(key);
        String mapKey = getMapKey(key);
        localKvStoreMapper.deleteById(mapKey);
        memoryCache.remove(mapKey);
        return previousValue;
    }

    @Override
    public boolean containsKey(Object key) {
        return get(key) != null;
    }

    @Override
    public boolean isEmpty() {
        return size() == 0;
    }

    @Override
    public int size() {
        // 这是一个简化实现，实际项目中可能需要更精确的计算
        return localKvStoreMapper.countByPrefix(name + ":");
    }

    @Override
    public void clear() {
        localKvStoreMapper.deleteByPrefix(name + ":");
        memoryCache.entrySet().removeIf(entry -> entry.getKey().startsWith(name + ":"));
    }

    @Override
    public Set<K> keySet() {
        List<String> keys = localKvStoreMapper.selectKeysByPrefix(name + ":");
        Set<K> result = new HashSet<>();
        for (String key : keys) {
            String actualKey = key.substring((name + ":").length());
            result.add((K) actualKey);
        }
        return result;
    }

    /**
     * Returns key set of this map.
     * Keys are loaded in batch. Batch size is defined by <code>count</code> param.
     *
     * @param count - size of keys batch
     * @return key set
     * @see #readAllKeySet()
     */
    @Override
    public Set<K> keySet(int count) {
        return Set.of();
    }

    /**
     * Returns key set of this map.
     * If <code>pattern</code> is not null then only keys match this pattern are loaded.
     * Keys are loaded in batch. Batch size is defined by <code>count</code> param.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * <p>
     * Supported glob-style patterns:
     * <p>
     * h?llo subscribes to hello, hallo and hxllo
     * <p>
     * h*llo subscribes to hllo and heeeello
     * <p>
     * h[ae]llo subscribes to hello and hallo, but not hillo
     *
     * @param pattern - key pattern
     * @param count   - size of keys batch
     * @return key set
     * @see #readAllKeySet()
     */
    @Override
    public Set<K> keySet(String pattern, int count) {
        return Set.of();
    }

    /**
     * Returns key set of this map.
     * If <code>pattern</code> is not null then only keys match this pattern are loaded.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * <p>
     * Supported glob-style patterns:
     * <p>
     * h?llo subscribes to hello, hallo and hxllo
     * <p>
     * h*llo subscribes to hllo and heeeello
     * <p>
     * h[ae]llo subscribes to hello and hallo, but not hillo
     *
     * @param pattern - key pattern
     * @return key set
     * @see #readAllKeySet()
     */
    @Override
    public Set<K> keySet(String pattern) {
        return Set.of();
    }

    @Override
    public Collection<V> values() {
        List<LocalKvStorePO> pos = localKvStoreMapper.selectByPrefix(name + ":");
        List<V> values = new ArrayList<>();
        for (LocalKvStorePO po : pos) {
            try {
                V value = (V) objectMapper.readValue(po.getValue(), Object.class);
                values.add(value);
            } catch (JsonProcessingException e) {
                log.error("Error deserializing value: {}", e.getMessage());
            }
        }
        return values;
    }

    /**
     * Returns values collection of this map.
     * Values are loaded in batch. Batch size is <code>10</code>.
     * If <code>keyPattern</code> is not null then only values mapped by matched keys of this pattern are loaded.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * Usage example:
     * <pre>
     *     Codec valueCodec = ...
     *     RMap<String, MyObject> map = redissonClient.getMap("simpleMap", new CompositeCodec(StringCodec.INSTANCE, valueCodec, valueCodec));
     *
     *     // or
     *
     *     RMap<String, String> map = redissonClient.getMap("simpleMap", StringCodec.INSTANCE);
     * </pre>
     * <pre>
     *  Supported glob-style patterns:
     *    h?llo subscribes to hello, hallo and hxllo
     *    h*llo subscribes to hllo and heeeello
     *    h[ae]llo subscribes to hello and hallo, but not hillo
     * </pre>
     *
     * @param keyPattern - key pattern
     * @return values collection
     * @see #readAllValues()
     */
    @Override
    public Collection<V> values(String keyPattern) {
        return List.of();
    }

    /**
     * Returns values collection of this map.
     * Values are loaded in batch. Batch size is defined by <code>count</code> param.
     * If <code>keyPattern</code> is not null then only values mapped by matched keys of this pattern are loaded.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * Usage example:
     * <pre>
     *     Codec valueCodec = ...
     *     RMap<String, MyObject> map = redissonClient.getMap("simpleMap", new CompositeCodec(StringCodec.INSTANCE, valueCodec, valueCodec));
     *
     *     // or
     *
     *     RMap<String, String> map = redissonClient.getMap("simpleMap", StringCodec.INSTANCE);
     * </pre>
     * <pre>
     *  Supported glob-style patterns:
     *    h?llo subscribes to hello, hallo and hxllo
     *    h*llo subscribes to hllo and heeeello
     *    h[ae]llo subscribes to hello and hallo, but not hillo
     * </pre>
     *
     * @param keyPattern - key pattern
     * @param count      - size of values batch
     * @return values collection
     * @see #readAllValues()
     */
    @Override
    public Collection<V> values(String keyPattern, int count) {
        return List.of();
    }

    /**
     * Returns values collection of this map.
     * Values are loaded in batch. Batch size is defined by <code>count</code> param.
     *
     * @param count - size of values batch
     * @return values collection
     * @see #readAllValues()
     */
    @Override
    public Collection<V> values(int count) {
        return List.of();
    }

    @Override
    public Set<Entry<K, V>> entrySet() {
        List<LocalKvStorePO> pos = localKvStoreMapper.selectByPrefix(name + ":");
        Set<Entry<K, V>> entries = new HashSet<>();
        for (LocalKvStorePO po : pos) {
            try {
                String actualKey = po.getKey().substring((name + ":").length());
                V value = (V) objectMapper.readValue(po.getValue(), Object.class);
                entries.add(new AbstractMap.SimpleEntry<>((K) actualKey, value));
            } catch (JsonProcessingException e) {
                log.error("Error deserializing value: {}", e.getMessage());
            }
        }
        return entries;
    }

    /**
     * Returns map entries collection.
     * Map entries are loaded in batch. Batch size is <code>10</code>.
     * If <code>keyPattern</code> is not null then only entries mapped by matched keys of this pattern are loaded.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * Usage example:
     * <pre>
     *     Codec valueCodec = ...
     *     RMap<String, MyObject> map = redissonClient.getMap("simpleMap", new CompositeCodec(StringCodec.INSTANCE, valueCodec, valueCodec));
     *
     *     // or
     *
     *     RMap<String, String> map = redissonClient.getMap("simpleMap", StringCodec.INSTANCE);
     * </pre>
     * <pre>
     *  Supported glob-style patterns:
     *    h?llo subscribes to hello, hallo and hxllo
     *    h*llo subscribes to hllo and heeeello
     *    h[ae]llo subscribes to hello and hallo, but not hillo
     * </pre>
     *
     * @param keyPattern key pattern
     * @return map entries collection
     * @see #readAllEntrySet()
     */
    @Override
    public Set<Entry<K, V>> entrySet(String keyPattern) {
        return Set.of();
    }

    /**
     * Returns map entries collection.
     * Map entries are loaded in batch. Batch size is defined by <code>count</code> param.
     * If <code>keyPattern</code> is not null then only entries mapped by matched keys of this pattern are loaded.
     * <p>
     * Use <code>org.redisson.client.codec.StringCodec</code> for Map keys.
     * <p>
     * Usage example:
     * <pre>
     *     Codec valueCodec = ...
     *     RMap<String, MyObject> map = redissonClient.getMap("simpleMap", new CompositeCodec(StringCodec.INSTANCE, valueCodec, valueCodec));
     *
     *     // or
     *
     *     RMap<String, String> map = redissonClient.getMap("simpleMap", StringCodec.INSTANCE);
     * </pre>
     * <pre>
     *  Supported glob-style patterns:
     *    h?llo subscribes to hello, hallo and hxllo
     *    h*llo subscribes to hllo and heeeello
     *    h[ae]llo subscribes to hello and hallo, but not hillo
     * </pre>
     *
     * @param keyPattern key pattern
     * @param count      size of entries batch
     * @return map entries collection
     * @see #readAllEntrySet()
     */
    @Override
    public Set<Entry<K, V>> entrySet(String keyPattern, int count) {
        return Set.of();
    }

    /**
     * Returns map entries collection.
     * Map entries are loaded in batch. Batch size is defined by <code>count</code> param.
     *
     * @param count - size of entries batch
     * @return map entries collection
     * @see #readAllEntrySet()
     */
    @Override
    public Set<Entry<K, V>> entrySet(int count) {
        return Set.of();
    }

    /**
     * Adds object event listener
     *
     * @param listener object event listener
     * @return listener id
     * @see TrackingListener
     * @see MapPutListener
     * @see MapRemoveListener
     * @see ExpiredObjectListener
     * @see DeletedObjectListener
     */
    @Override
    public int addListener(ObjectListener listener) {
        return 0;
    }

    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        return expireAsync(Duration.ofMillis(timeUnit.toMillis(timeToLive))).join();
    }

    @Override
    public RFuture<Boolean> expireAsync(Duration duration) {
        long expireTime = System.currentTimeMillis() + duration.toMillis();
        List<String> keys = localKvStoreMapper.selectKeysByPrefix(name + ":");
        
        for (String key : keys) {
            LocalKvStorePO po = localKvStoreMapper.selectById(key);
            if (po != null) {
                po.setExpireTime(expireTime);  
                localKvStoreMapper.updateById(po);
            }
        }
        
        return new CompletableFutureWrapper(CompletableFuture.completedFuture(true));
    }

    /**
     * Sets a timeout for this object only if it has been already set.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfSetAsync(Duration duration) {
        return null;
    }

    /**
     * Sets a timeout for this object only if it hasn't been set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfNotSetAsync(Duration duration) {
        return null;
    }

    /**
     * Sets a timeout for this object only if it's greater than timeout set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfGreaterAsync(Duration duration) {
        return null;
    }

    /**
     * Sets a timeout for this object only if it's less than timeout set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfLessAsync(Duration duration) {
        return null;
    }

    private void putValue(K key, V value) {
        putValue(key, value, null);
    }

    private void putValue(K key, V value, Long expireTime) {
        String mapKey = getMapKey(key);
        try {
            LocalKvStorePO po = new LocalKvStorePO();
            po.setKey(mapKey);
            po.setValue(objectMapper.writeValueAsString(value));
            po.setExpireTime(expireTime);
            localKvStoreMapper.insertOrUpdate(po);
            memoryCache.put(mapKey, value);
        } catch (JsonProcessingException e) {
            log.error("Error serializing value for key {}: {}", mapKey, e.getMessage());
        }
    }

    // === 以下方法为了完整实现接口，部分采用简化实现 ===

    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        for (Entry<? extends K, ? extends V> entry : m.entrySet()) {
            put(entry.getKey(), entry.getValue());
        }
    }

    /**
     * Stores map entries specified in <code>map</code> object in batch mode.
     * Batch inserted by chunks limited by <code>batchSize</code> value
     * to avoid OOM and/or Redis response timeout error for map with big size.
     * <p>
     * If {@link MapWriter} is defined then map entries are stored in write-through mode.
     *
     * @param map       mappings to be stored in this map
     * @param batchSize - size of map entries batch
     */
    @Override
    public void putAll(Map<? extends K, ? extends V> map, int batchSize) {

    }

    /**
     * Returns map slice contained the mappings with defined <code>keys</code>.
     * <p>
     * If map doesn't contain value/values for specified key/keys and {@link MapLoader} is defined
     * then value/values will be loaded in read-through mode.
     * <p>
     * The returned map is <b>NOT</b> backed by the original map.
     *
     * @param keys map keys
     * @return Map slice
     */
    @Override
    public Map<K, V> getAll(Set<K> keys) {
        return Map.of();
    }

    /**
     * Removes map entries mapped by specified <code>keys</code>.
     * <p>
     * Works faster than <code>{@link #remove(Object)}</code> but not returning
     * the value.
     * <p>
     * If {@link MapWriter} is defined then <code>keys</code>are deleted in write-through mode.
     *
     * @param keys - map keys
     * @return the number of keys that were removed from the hash, not including specified but non existing keys
     */
    @Override
    public long fastRemove(K... keys) {
        return 0;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>.
     * <p>
     * Works faster than <code>{@link #put(Object, Object)}</code> but not returning
     * previous value.
     * <p>
     * Returns <code>true</code> if key is a new key in the hash and value was set or
     * <code>false</code> if key already exists in the hash and the value was updated.
     * <p>
     * If {@link MapWriter} is defined then map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key is a new key in the hash and value was set.
     * <code>false</code> if key already exists in the hash and the value was updated.
     */
    @Override
    public boolean fastPut(K key, V value) {
        return false;
    }

    /**
     * Replaces previous value with a new <code>value</code> mapped by specified <code>key</code>.
     * <p>
     * Works faster than <code>{@link #replace(Object, Object)}</code> but not returning
     * the previous value.
     * <p>
     * Returns <code>true</code> if key exists and value was updated or
     * <code>false</code> if key doesn't exists and value wasn't updated.
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key exists and value was updated.
     * <code>false</code> if key doesn't exists and value wasn't updated.
     */
    @Override
    public boolean fastReplace(K key, V value) {
        return false;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>
     * only if there is no value with specified<code>key</code> stored before.
     * <p>
     * Returns <code>true</code> if key is a new one in the hash and value was set or
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     * <p>
     * Works faster than <code>{@link #putIfAbsent(Object, Object)}</code> but not returning
     * the previous value associated with <code>key</code>
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key is a new one in the hash and value was set.
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     */
    @Override
    public boolean fastPutIfAbsent(K key, V value) {
        return false;
    }

    /**
     * Stores the specified <code>value</code> mapped by <code>key</code>
     * only if mapping already exists.
     * <p>
     * Returns <code>true</code> if key is a new one in the hash and value was set or
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     * <p>
     * Works faster than <code>{@link #putIfExists(Object, Object)}</code> but doesn't return
     * previous value associated with <code>key</code>
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key already exists in the hash and new value has been stored.
     * <code>false</code> if key doesn't exist in the hash and value hasn't been set.
     */
    @Override
    public boolean fastPutIfExists(K key, V value) {
        return false;
    }

    /**
     * Read all keys at once
     *
     * @return keys
     */
    @Override
    public Set<K> readAllKeySet() {
        return Set.of();
    }

    /**
     * Read all values at once
     *
     * @return values
     */
    @Override
    public Collection<V> readAllValues() {
        return List.of();
    }

    /**
     * Read all map entries at once
     *
     * @return entries
     */
    @Override
    public Set<Entry<K, V>> readAllEntrySet() {
        return Set.of();
    }

    /**
     * Read all map as local instance at once
     *
     * @return map
     */
    @Override
    public Map<K, V> readAllMap() {
        return Map.of();
    }

    @Override
    public boolean containsValue(Object value) {
        return values().contains(value);
    }

    @Override
    public V replace(K key, V value) {
        if (containsKey(key)) {
            return put(key, value);
        }
        return null;
    }

    @Override
    public boolean replace(K key, V oldValue, V newValue) {
        V currentValue = get(key);
        if (Objects.equals(currentValue, oldValue)) {
            put(key, newValue);
            return true;
        }
        return false;
    }

    /**
     * Removes map entry only if it exists with specified <code>key</code> and <code>value</code>.
     * <p>
     * If {@link MapWriter} is defined then <code>key</code>is deleted in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if map entry has been removed otherwise <code>false</code>.
     */
    @Override
    public boolean remove(Object key, Object value) {
        return false;
    }

    @Override
    public V compute(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V oldValue = get(key);
        V newValue = remappingFunction.apply(key, oldValue);
        if (newValue != null) {
            put(key, newValue);
        } else if (oldValue != null) {
            remove(key);
        }
        return newValue;
    }

    @Override
    public V computeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        V value = get(key);
        if (value == null) {
            value = mappingFunction.apply(key);
            if (value != null) {
                put(key, value);
            }
        }
        return value;
    }

    @Override
    public V computeIfPresent(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V oldValue = get(key);
        if (oldValue != null) {
            V newValue = remappingFunction.apply(key, oldValue);
            if (newValue != null) {
                put(key, newValue);
            } else {
                remove(key);
            }
            return newValue;
        }
        return null;
    }

    @Override
    public V merge(K key, V value, BiFunction<? super V, ? super V, ? extends V> remappingFunction) {
        V oldValue = get(key);
        V newValue = (oldValue == null) ? value : remappingFunction.apply(oldValue, value);
        if (newValue != null) {
            put(key, newValue);
        } else {
            remove(key);
        }
        return newValue;
    }

    // === 以下方法在本地模式下不支持，抛出异常 ===

    @Override
    public <KOut, VOut> RMapReduce<K, V, KOut, VOut> mapReduce() {
        throw new UnsupportedOperationException("MapReduce not supported in local mode");
    }

    /**
     * Returns <code>RCountDownLatch</code> instance associated with key
     *
     * @param key - map key
     * @return countdownlatch
     */
    @Override
    public RCountDownLatch getCountDownLatch(K key) {
        return null;
    }

    /**
     * Returns <code>RPermitExpirableSemaphore</code> instance associated with key
     *
     * @param key - map key
     * @return permitExpirableSemaphore
     */
    @Override
    public RPermitExpirableSemaphore getPermitExpirableSemaphore(K key) {
        return null;
    }

    /**
     * Returns <code>RSemaphore</code> instance associated with key
     *
     * @param key - map key
     * @return semaphore
     */
    @Override
    public RSemaphore getSemaphore(K key) {
        return null;
    }

    /**
     * Returns <code>RLock</code> instance associated with key
     *
     * @param key - map key
     * @return fairlock
     */
    @Override
    public RLock getFairLock(K key) {
        return null;
    }

    /**
     * Returns <code>RReadWriteLock</code> instance associated with key
     *
     * @param key - map key
     * @return readWriteLock
     */
    @Override
    public RReadWriteLock getReadWriteLock(K key) {
        return null;
    }

    /**
     * Returns <code>RLock</code> instance associated with key
     *
     * @param key - map key
     * @return lock
     */
    @Override
    public RLock getLock(K key) {
        return null;
    }

    /**
     * Returns size of value mapped by specified <code>key</code> in bytes
     *
     * @param key - map key
     * @return size of value
     */
    @Override
    public int valueSize(K key) {
        return 0;
    }

    /**
     * Adds the given <code>delta</code> to the current value
     * by mapped <code>key</code>.
     * <p>
     * Works only with codecs below
     * <p>
     * {@link JsonJacksonCodec},
     * <p>
     * {@link StringCodec},
     * <p>
     * {@link IntegerCodec},
     * <p>
     * {@link DoubleCodec}
     * <p>
     * {@link LongCodec}
     *
     * @param key   - map key
     * @param delta the value to add
     * @return the updated value
     */
    @Override
    public V addAndGet(K key, Number delta) {
        return null;
    }

    @Override
    public Codec getCodec() {
        throw new UnsupportedOperationException("Codec not supported in local mode");
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public boolean isExists() {
        return !isEmpty();
    }

    @Override
    public boolean delete() {
        clear();
        return true;
    }

    @Override
    public boolean unlink() {
        return delete();
    }

    @Override
    public long remainTimeToLive() {
        throw new UnsupportedOperationException("remainTimeToLive not supported in local mode");
    }

    /**
     * Returns expiration time of the object as the absolute Unix expiration timestamp in milliseconds.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @return Unix time in milliseconds
     * -2 if the key does not exist.
     * -1 if the key exists but has no associated expiration time.
     */
    @Override
    public long getExpireTime() {
        return 0;
    }

    @Override
    public boolean clearExpire() {
        throw new UnsupportedOperationException("clearExpire not supported in local mode");
    }

    @Override
    public boolean expireAt(long timestamp) {
        throw new UnsupportedOperationException("expireAt not supported in local mode");
    }

    @Override
    public boolean expireAt(Date timestamp) {
        throw new UnsupportedOperationException("expireAt not supported in local mode");
    }

    /**
     * Sets an expiration date for this object. When expire date comes
     * the key will automatically be deleted.
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expire(Instant time) {
        return false;
    }

    /**
     * Sets an expiration date for this object only if it has been already set.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfSet(Instant time) {
        return false;
    }

    /**
     * Sets an expiration date for this object only if it hasn't been set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfNotSet(Instant time) {
        return false;
    }

    /**
     * Sets an expiration date for this object only if it's greater than expiration date set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfGreater(Instant time) {
        return false;
    }

    /**
     * Sets an expiration date for this object only if it's less than expiration date set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfLess(Instant time) {
        return false;
    }

    /**
     * Sets a timeout for this object. After the timeout has expired,
     * the key will automatically be deleted.
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expire(Duration duration) {
        return false;
    }

    /**
     * Sets a timeout for this object only if it has been already set.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfSet(Duration duration) {
        return false;
    }

    /**
     * Sets a timeout for this object only if it hasn't been set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfNotSet(Duration duration) {
        return false;
    }

    /**
     * Sets a timeout for this object only if it's greater than timeout set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfGreater(Duration duration) {
        return false;
    }

    /**
     * Sets a timeout for this object only if it's less than timeout set before.
     * After the timeout has expired, the key will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param duration timeout before object will be deleted
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public boolean expireIfLess(Duration duration) {
        return false;
    }

    @Override
    public RFuture<Boolean> expireAtAsync(Date timestamp) {
        throw new UnsupportedOperationException("expireAtAsync not supported in local mode");
    }

    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        throw new UnsupportedOperationException("expireAtAsync not supported in local mode");
    }

    /**
     * Set an expire date for object. When expire date comes
     * the key will automatically be deleted.
     *
     * @param time - expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireAsync(Instant time) {
        return null;
    }

    /**
     * Sets an expiration date for this object only if it has been already set.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfSetAsync(Instant time) {
        return null;
    }

    /**
     * Sets an expiration date for this object only if it hasn't been set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfNotSetAsync(Instant time) {
        return null;
    }

    /**
     * Sets an expiration date for this object only if it's greater than expiration date set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfGreaterAsync(Instant time) {
        return null;
    }

    /**
     * Sets an expiration date for this object only if it's less than expiration date set before.
     * When expire date comes the object will automatically be deleted.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @param time expire date
     * @return <code>true</code> if the timeout was set and <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> expireIfLessAsync(Instant time) {
        return null;
    }

    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return expireAsync(Duration.ofMillis(timeUnit.toMillis(timeToLive)));
    }

    @Override
    public RFuture<Boolean> clearExpireAsync() {
        throw new UnsupportedOperationException("clearExpireAsync not supported in local mode");
    }

    @Override
    public void rename(String newName) {
        throw new UnsupportedOperationException("rename not supported in local mode");
    }

    @Override
    public RFuture<Void> renameAsync(String newName) {
        throw new UnsupportedOperationException("renameAsync not supported in local mode");
    }

    @Override
    public boolean renamenx(String newName) {
        throw new UnsupportedOperationException("renamenx not supported in local mode");
    }

    @Override
    public RFuture<Boolean> renamenxAsync(String newName) {
        throw new UnsupportedOperationException("renamenxAsync not supported in local mode");
    }

    @Override
    public void migrate(String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("migrate not supported in local mode");
    }

    @Override
    public RFuture<Void> migrateAsync(String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("migrateAsync not supported in local mode");
    }

    @Override
    public void copy(String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("copy not supported in local mode");
    }

    /**
     * Copy this object instance to the new instance with a defined name.
     *
     * @param destination name of the destination instance
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public boolean copy(String destination) {
        return false;
    }

    /**
     * Copy this object instance to the new instance with a defined name and database.
     *
     * @param destination name of the destination instance
     * @param database    database number
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public boolean copy(String destination, int database) {
        return false;
    }

    /**
     * Copy this object instance to the new instance with a defined name, and replace it if it already exists.
     *
     * @param destination name of the destination instance
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public boolean copyAndReplace(String destination) {
        return false;
    }

    /**
     * Copy this object instance to the new instance with a defined name and database, and replace it if it already exists.
     *
     * @param destination name of the destination instance
     * @param database    database number
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public boolean copyAndReplace(String destination, int database) {
        return false;
    }

    /**
     * Move object to another database
     *
     * @param database - Redis database number
     * @return <code>true</code> if key was moved else <code>false</code>
     */
    @Override
    public boolean move(int database) {
        return false;
    }

    @Override
    public RFuture<Void> copyAsync(String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("copyAsync not supported in local mode");
    }

    /**
     * Copy this object instance to the new instance with a defined name.
     *
     * @param destination name of the destination instance
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public RFuture<Boolean> copyAsync(String destination) {
        return null;
    }

    /**
     * Copy this object instance to the new instance with a defined name and database.
     *
     * @param destination name of the destination instance
     * @param database    database number
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public RFuture<Boolean> copyAsync(String destination, int database) {
        return null;
    }

    /**
     * Copy this object instance to the new instance with a defined name, and replace it if it already exists.
     *
     * @param destination name of the destination instance
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public RFuture<Boolean> copyAndReplaceAsync(String destination) {
        return null;
    }

    /**
     * Copy this object instance to the new instance with a defined name and database, and replace it if it already exists.
     *
     * @param destination name of the destination instance
     * @param database    database number
     * @return <code>true</code> if this object instance was copied else <code>false</code>
     */
    @Override
    public RFuture<Boolean> copyAndReplaceAsync(String destination, int database) {
        return null;
    }

    /**
     * Move object to another database in async mode
     *
     * @param database - number of Redis database
     * @return <code>true</code> if key was moved <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> moveAsync(int database) {
        return null;
    }

    /**
     * Returns number of seconds spent since last write or read operation over this object.
     *
     * @return number of seconds
     */
    @Override
    public Long getIdleTime() {
        return 0L;
    }

    /**
     * Returns bytes amount used by object in Redis memory.
     *
     * @return size in bytes
     */
    @Override
    public long sizeInMemory() {
        return 0;
    }

    @Override
    public void restore(byte[] state) {
        throw new UnsupportedOperationException("restore not supported in local mode");
    }

    @Override
    public void restore(byte[] state, long timeToLive, TimeUnit timeUnit) {
        throw new UnsupportedOperationException("restore not supported in local mode");
    }

    @Override
    public void restoreAndReplace(byte[] state) {
        throw new UnsupportedOperationException("restoreAndReplace not supported in local mode");
    }

    @Override
    public void restoreAndReplace(byte[] state, long timeToLive, TimeUnit timeUnit) {
        throw new UnsupportedOperationException("restoreAndReplace not supported in local mode");
    }

    @Override
    public RFuture<Void> restoreAndReplaceAsync(byte[] state) {
        throw new UnsupportedOperationException("restoreAndReplaceAsync not supported in local mode");
    }

    @Override
    public RFuture<Void> restoreAndReplaceAsync(byte[] state, long timeToLive, TimeUnit timeUnit) {
        throw new UnsupportedOperationException("restoreAndReplaceAsync not supported in local mode");
    }

    /**
     * Returns number of seconds spent since last write or read operation over this object.
     *
     * @return number of seconds
     */
    @Override
    public RFuture<Long> getIdleTimeAsync() {
        return null;
    }

    /**
     * Returns bytes amount used by object in Redis memory.
     *
     * @return size in bytes
     */
    @Override
    public RFuture<Long> sizeInMemoryAsync() {
        return null;
    }

    @Override
    public RFuture<Void> restoreAsync(byte[] state) {
        throw new UnsupportedOperationException("restoreAsync not supported in local mode");
    }

    @Override
    public RFuture<Void> restoreAsync(byte[] state, long timeToLive, TimeUnit timeUnit) {
        throw new UnsupportedOperationException("restoreAsync not supported in local mode");
    }

    @Override
    public byte[] dump() {
        throw new UnsupportedOperationException("dump not supported in local mode");
    }

    @Override
    public RFuture<byte[]> dumpAsync() {
        throw new UnsupportedOperationException("dumpAsync not supported in local mode");
    }

    @Override
    public boolean touch() {
        throw new UnsupportedOperationException("touch not supported in local mode");
    }

    @Override
    public RFuture<Boolean> touchAsync() {
        throw new UnsupportedOperationException("touchAsync not supported in local mode");
    }

    @Override
    public RFuture<Boolean> unlinkAsync() {
        throw new UnsupportedOperationException("unlinkAsync not supported in local mode");
    }

    @Override
    public RFuture<Boolean> deleteAsync() {
        throw new UnsupportedOperationException("deleteAsync not supported in local mode");
    }

    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        throw new UnsupportedOperationException("remainTimeToLiveAsync not supported in local mode");
    }

    /**
     * Returns expiration time of the object as the absolute Unix expiration timestamp in milliseconds.
     * <p>
     * Requires <b>Redis 7.0.0 and higher.</b>
     *
     * @return Unix time in milliseconds
     * -2 if the key does not exist.
     * -1 if the key exists but has no associated expiration time.
     */
    @Override
    public RFuture<Long> getExpireTimeAsync() {
        return null;
    }

    @Override
    public RFuture<Boolean> isExistsAsync() {
        throw new UnsupportedOperationException("isExistsAsync not supported in local mode");
    }

    /**
     * Adds object event listener
     *
     * @param listener - object event listener
     * @return listener id
     * @see ExpiredObjectListener
     * @see DeletedObjectListener
     */
    @Override
    public RFuture<Integer> addListenerAsync(ObjectListener listener) {
        return null;
    }

    /**
     * Removes object event listener
     *
     * @param listenerId - listener id
     */
    @Override
    public RFuture<Void> removeListenerAsync(int listenerId) {
        return null;
    }

    @Override
    public void removeListener(int listenerId) {
        throw new UnsupportedOperationException("removeListener not supported in local mode");
    }


    /**
     * Destroys object when it's not necessary anymore.
     */
    @Override
    public void destroy() {

    }

    /**
     * Associates specified key with the given value if key isn't already associated with a value.
     * Otherwise, replaces the associated value with the results of the given
     * remapping function, or removes if the result is {@code null}.
     *
     * @param key               - map key
     * @param value             - value to be merged with the existing value
     *                          associated with the key or to be associated with the key,
     *                          if no existing value
     * @param remappingFunction - the function is invoked with the existing value to compute new value
     * @return new value associated with the specified key or
     * {@code null} if no value associated with the key
     */
    @Override
    public RFuture<V> mergeAsync(K key, V value, BiFunction<? super V, ? super V, ? extends V> remappingFunction) {
        return null;
    }

    /**
     * Computes a new mapping for the specified key and its current mapped value.
     *
     * @param key               - map key
     * @param remappingFunction - function to compute a value
     * @return the new value associated with the specified key, or {@code null} if none
     */
    @Override
    public RFuture<V> computeAsync(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        return null;
    }

    /**
     * Computes a mapping for the specified key if it's not mapped before.
     *
     * @param key             - map key
     * @param mappingFunction - function to compute a value
     * @return current or new computed value associated with
     * the specified key, or {@code null} if the computed value is null
     */
    @Override
    public RFuture<V> computeIfAbsentAsync(K key, Function<? super K, ? extends V> mappingFunction) {
        return null;
    }

    /**
     * Computes a mapping for the specified key only if it's already mapped.
     *
     * @param key               - map key
     * @param remappingFunction - function to compute a value
     * @return the new value associated with the specified key, or null if none
     */
    @Override
    public RFuture<V> computeIfPresentAsync(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        return null;
    }

    /**
     * Loads all map entries to this Redis map using {@link MapLoader}.
     *
     * @param replaceExistingValues - <code>true</code> if existed values should be replaced, <code>false</code> otherwise.
     * @param parallelism           - parallelism level, used to increase speed of process execution
     * @return void
     */
    @Override
    public RFuture<Void> loadAllAsync(boolean replaceExistingValues, int parallelism) {
        return null;
    }

    /**
     * Loads map entries using {@link MapLoader} whose keys are listed in defined <code>keys</code> parameter.
     *
     * @param keys                  - map keys
     * @param replaceExistingValues - <code>true</code> if existed values should be replaced, <code>false</code> otherwise.
     * @param parallelism           - parallelism level, used to increase speed of process execution
     * @return void
     */
    @Override
    public RFuture<Void> loadAllAsync(Set<? extends K> keys, boolean replaceExistingValues, int parallelism) {
        return null;
    }

    /**
     * Returns size of value mapped by key in bytes
     *
     * @param key - map key
     * @return size of value
     */
    @Override
    public RFuture<Integer> valueSizeAsync(K key) {
        return null;
    }

    /**
     * Returns map slice contained the mappings with defined <code>keys</code>.
     * <p>
     * If map doesn't contain value/values for specified key/keys and {@link MapLoader} is defined
     * then value/values will be loaded in read-through mode.
     * <p>
     * The returned map is <b>NOT</b> backed by the original map.
     *
     * @param keys - map keys
     * @return Map slice
     */
    @Override
    public RFuture<Map<K, V>> getAllAsync(Set<K> keys) {
        return null;
    }

    /**
     * Stores map entries specified in <code>map</code> object in batch mode.
     * <p>
     * If {@link MapWriter} is defined then map entries will be stored in write-through mode.
     *
     * @param map mappings to be stored in this map
     * @return void
     */
    @Override
    public RFuture<Void> putAllAsync(Map<? extends K, ? extends V> map) {
        return null;
    }

    /**
     * Stores map entries specified in <code>map</code> object in batch mode.
     * Batch inserted by chunks limited by <code>batchSize</code> value
     * to avoid OOM and/or Redis response timeout error for map with big size.
     * <p>
     * If {@link MapWriter} is defined then map entries are stored in write-through mode.
     *
     * @param map       mappings to be stored in this map
     * @param batchSize - size of map entries batch
     * @return void
     */
    @Override
    public RFuture<Void> putAllAsync(Map<? extends K, ? extends V> map, int batchSize) {
        return null;
    }

    /**
     * Returns random keys from this map limited by <code>count</code>
     *
     * @param count - keys amount to return
     * @return random keys
     */
    @Override
    public RFuture<Set<K>> randomKeysAsync(int count) {
        return null;
    }

    /**
     * Returns random map entries from this map limited by <code>count</code>
     *
     * @param count - entries amount to return
     * @return random entries
     */
    @Override
    public RFuture<Map<K, V>> randomEntriesAsync(int count) {
        return null;
    }

    /**
     * Adds the given <code>delta</code> to the current value
     * by mapped <code>key</code>.
     * <p>
     * Works only with codecs below
     * <p>
     * {@link JsonJacksonCodec},
     * <p>
     * {@link StringCodec},
     * <p>
     * {@link IntegerCodec},
     * <p>
     * {@link DoubleCodec}
     * <p>
     * {@link LongCodec}
     *
     * @param key   - map key
     * @param delta the value to add
     * @return the updated value
     */
    @Override
    public RFuture<V> addAndGetAsync(K key, Number delta) {
        return null;
    }

    /**
     * Returns <code>true</code> if this map contains any map entry
     * with specified <code>value</code>, otherwise <code>false</code>
     *
     * @param value - map value
     * @return <code>true</code> if this map contains any map entry
     * with specified <code>value</code>, otherwise <code>false</code>
     */
    @Override
    public RFuture<Boolean> containsValueAsync(Object value) {
        return null;
    }

    /**
     * Returns <code>true</code> if this map contains map entry
     * mapped by specified <code>key</code>, otherwise <code>false</code>
     *
     * @param key - map key
     * @return <code>true</code> if this map contains map entry
     * mapped by specified <code>key</code>, otherwise <code>false</code>
     */
    @Override
    public RFuture<Boolean> containsKeyAsync(Object key) {
        return null;
    }

    /**
     * Returns size of this map
     *
     * @return size
     */
    @Override
    public RFuture<Integer> sizeAsync() {
        return null;
    }

    /**
     * Removes map entries mapped by specified <code>keys</code>.
     * <p>
     * Works faster than <code>{@link #removeAsync(Object)}</code> but not returning
     * the value.
     * <p>
     * If {@link MapWriter} is defined then <code>keys</code>are deleted in write-through mode.
     *
     * @param keys - map keys
     * @return the number of keys that were removed from the hash, not including specified but non existing keys
     */
    @Override
    public RFuture<Long> fastRemoveAsync(K... keys) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>.
     * <p>
     * Works faster than <code>{@link #putAsync(Object, Object)}</code> but not returning
     * previous value.
     * <p>
     * Returns <code>true</code> if key is a new key in the hash and value was set or
     * <code>false</code> if key already exists in the hash and the value was updated.
     * <p>
     * If {@link MapWriter} is defined then map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key is a new key in the hash and value was set.
     * <code>false</code> if key already exists in the hash and the value was updated.
     */
    @Override
    public RFuture<Boolean> fastPutAsync(K key, V value) {
        return null;
    }

    /**
     * Replaces previous value with a new <code>value</code> mapped by specified <code>key</code>.
     * <p>
     * Works faster than <code>{@link #replaceAsync(Object, Object)}</code> but not returning
     * the previous value.
     * <p>
     * Returns <code>true</code> if key exists and value was updated or
     * <code>false</code> if key doesn't exists and value wasn't updated.
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key exists and value was updated.
     * <code>false</code> if key doesn't exists and value wasn't updated.
     */
    @Override
    public RFuture<Boolean> fastReplaceAsync(K key, V value) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>
     * only if there is no value with specified<code>key</code> stored before.
     * <p>
     * Returns <code>true</code> if key is a new one in the hash and value was set or
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     * <p>
     * Works faster than <code>{@link #putIfAbsentAsync(Object, Object)}</code> but not returning
     * the previous value associated with <code>key</code>
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key is a new one in the hash and value was set.
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     */
    @Override
    public RFuture<Boolean> fastPutIfAbsentAsync(K key, V value) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by <code>key</code>
     * only if mapping already exists.
     * <p>
     * Returns <code>true</code> if key is a new one in the hash and value was set or
     * <code>false</code> if key already exists in the hash and change hasn't been made.
     * <p>
     * Works faster than <code>{@link #putIfExistsAsync(Object, Object)}</code> but doesn't return
     * previous value associated with <code>key</code>
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if key already exists in the hash and new value has been stored.
     * <code>false</code> if key doesn't exist in the hash and value hasn't been set.
     */
    @Override
    public RFuture<Boolean> fastPutIfExistsAsync(K key, V value) {
        return null;
    }

    /**
     * Read all keys at once
     *
     * @return keys
     */
    @Override
    public RFuture<Set<K>> readAllKeySetAsync() {
        return null;
    }

    /**
     * Read all values at once
     *
     * @return values
     */
    @Override
    public RFuture<Collection<V>> readAllValuesAsync() {
        return null;
    }

    /**
     * Read all map entries at once
     *
     * @return entries
     */
    @Override
    public RFuture<Set<Entry<K, V>>> readAllEntrySetAsync() {
        return null;
    }

    /**
     * Read all map as local instance at once
     *
     * @return map
     */
    @Override
    public RFuture<Map<K, V>> readAllMapAsync() {
        return null;
    }

    /**
     * Returns the value mapped by defined <code>key</code> or {@code null} if value is absent.
     * <p>
     * If map doesn't contain value for specified key and {@link MapLoader} is defined
     * then value will be loaded in read-through mode.
     *
     * @param key the key
     * @return the value mapped by defined <code>key</code> or {@code null} if value is absent
     */
    @Override
    public RFuture<V> getAsync(K key) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>.
     * Returns previous value if map entry with specified <code>key</code> already existed.
     * <p>
     * If {@link MapWriter} is defined then map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return previous associated value
     */
    @Override
    public RFuture<V> putAsync(K key, V value) {
        return null;
    }

    /**
     * Removes map entry by specified <code>key</code> and returns value.
     * <p>
     * If {@link MapWriter} is defined then <code>key</code>is deleted in write-through mode.
     *
     * @param key - map key
     * @return deleted value, <code>null</code> if map entry doesn't exist
     */
    @Override
    public RFuture<V> removeAsync(K key) {
        return null;
    }

    /**
     * Replaces previous value with a new <code>value</code> mapped by specified <code>key</code>.
     * Returns <code>null</code> if there is no map entry stored before and doesn't store new map entry.
     * <p>
     * If {@link MapWriter} is defined then new <code>value</code>is written in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return previous associated value
     * or <code>null</code> if there is no map entry stored before and doesn't store new map entry
     */
    @Override
    public RFuture<V> replaceAsync(K key, V value) {
        return null;
    }

    /**
     * Replaces previous <code>oldValue</code> with a <code>newValue</code> mapped by specified <code>key</code>.
     * Returns <code>false</code> if previous value doesn't exist or equal to <code>oldValue</code>.
     * <p>
     * If {@link MapWriter} is defined then <code>newValue</code>is written in write-through mode.
     *
     * @param key      - map key
     * @param oldValue - map old value
     * @param newValue - map new value
     * @return <code>true</code> if value has been replaced otherwise <code>false</code>.
     */
    @Override
    public RFuture<Boolean> replaceAsync(K key, V oldValue, V newValue) {
        return null;
    }

    /**
     * Removes map entry only if it exists with specified <code>key</code> and <code>value</code>.
     * <p>
     * If {@link MapWriter} is defined then <code>key</code>is deleted in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>true</code> if map entry has been removed otherwise <code>false</code>.
     */
    @Override
    public RFuture<Boolean> removeAsync(Object key, Object value) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by specified <code>key</code>
     * only if there is no value with specified<code>key</code> stored before.
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>null</code> if key is a new one in the hash and value was set.
     * Previous value if key already exists in the hash and change hasn't been made.
     */
    @Override
    public RFuture<V> putIfAbsentAsync(K key, V value) {
        return null;
    }

    /**
     * Stores the specified <code>value</code> mapped by <code>key</code>
     * only if mapping already exists.
     * <p>
     * If {@link MapWriter} is defined then new map entry is stored in write-through mode.
     *
     * @param key   - map key
     * @param value - map value
     * @return <code>null</code> if key is doesn't exists in the hash and value hasn't been set.
     * Previous value if key already exists in the hash and new value has been stored.
     */
    @Override
    public RFuture<V> putIfExistsAsync(K key, V value) {
        return null;
    }

    /**
     * Clears map without removing options data used during map creation.
     *
     * @return <code>true</code> if map was cleared <code>false</code> if not
     */
    @Override
    public RFuture<Boolean> clearAsync() {
        return null;
    }
}