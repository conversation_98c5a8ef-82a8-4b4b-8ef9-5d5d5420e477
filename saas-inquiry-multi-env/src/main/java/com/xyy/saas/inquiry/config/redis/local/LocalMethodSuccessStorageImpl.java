package com.xyy.saas.inquiry.config.redis.local;

import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.eventbus.rocketmq.storage.AbstractMethodSuccessStorage;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStorePO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地实现的 MethodSuccessStorage
 * 直接使用 H2 数据库存储，不依赖 Redisson
 * 
 * 这是一个备选实现，当不希望使用 LocalRedissonClient 时可以启用
 */
@Slf4j
@Component
@Profile("local")
@RequiredArgsConstructor
public class LocalMethodSuccessStorageImpl extends AbstractMethodSuccessStorage {

    private final LocalKvStoreMapper localKvStoreMapper;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 内存缓存，提高性能
    private final Map<String, Set<String>> methodCache = new ConcurrentHashMap<>();

    @Override
    public Set<String> getSuccessMethod(EventBusAbstractMessage abstractMessage) {
        String msgId = abstractMessage.getMsgId();
        
        // 先检查内存缓存
        Set<String> cachedMethods = methodCache.get(msgId);
        if (cachedMethods != null) {
            return cachedMethods;
        }

        // 从数据库查询
        String keyPrefix = getEventBusPrefix(msgId);
        List<LocalKvStorePO> records = localKvStoreMapper.selectByPrefix(keyPrefix);
        
        Set<String> successMethods = new HashSet<>();
        for (LocalKvStorePO record : records) {
            // 检查是否过期
            if (record.getExpireTime() != null && System.currentTimeMillis() > record.getExpireTime()) {
                localKvStoreMapper.deleteById(record.getKey());
                continue;
            }
            
            // 提取方法ID
            String methodId = extractMethodId(record.getKey(), keyPrefix);
            if (methodId != null) {
                successMethods.add(methodId);
            }
        }
        
        // 缓存结果
        methodCache.put(msgId, successMethods);
        return successMethods;
    }

    @Override
    public boolean successMethod(String messageId, Method method) {
        String methodKey = getMethodKey(messageId, method);
        LocalKvStorePO record = localKvStoreMapper.selectById(methodKey);
        
        if (record == null) {
            return false;
        }
        
        // 检查是否过期
        if (record.getExpireTime() != null && System.currentTimeMillis() > record.getExpireTime()) {
            localKvStoreMapper.deleteById(methodKey);
            return false;
        }
        
        return true;
    }

    @Override
    public void saveSuccessMethod(String messageId, Method method) {
        String methodKey = getMethodKey(messageId, method);
        String methodId = getMethodId(method);
        
        try {
            LocalKvStorePO record = new LocalKvStorePO();
            record.setKey(methodKey);
            record.setValue("1"); // 简单标记值
            // 设置3小时过期时间
            record.setExpireTime(System.currentTimeMillis() + 3 * 60 * 60 * 1000L);
            
            localKvStoreMapper.insertOrUpdate(record);
            
            // 更新内存缓存
            String msgId = messageId;
            methodCache.computeIfAbsent(msgId, k -> new HashSet<>()).add(methodId);
            
        } catch (Exception e) {
            log.error("Error saving success method for messageId {}, method {}: {}", 
                messageId, method.getName(), e.getMessage());
        }
    }

    /**
     * 生成方法存储的键
     */
    private String getMethodKey(String messageId, Method method) {
        return getEventBusPrefix(messageId) + getMethodId(method);
    }

    /**
     * 获取事件总线前缀
     */
    private String getEventBusPrefix(String messageId) {
        return "EVENT_BUS:" + messageId + ":";
    }

    /**
     * 从完整的键中提取方法ID
     */
    private String extractMethodId(String fullKey, String prefix) {
        if (fullKey.startsWith(prefix)) {
            return fullKey.substring(prefix.length());
        }
        return null;
    }

    /**
     * 清理过期的缓存条目
     * 可以通过定时任务调用
     */
    public void cleanupExpiredEntries() {
        try {
            // 清理数据库中过期的记录
            List<LocalKvStorePO> allRecords = localKvStoreMapper.selectByPrefix("EVENT_BUS:");
            long now = System.currentTimeMillis();
            
            for (LocalKvStorePO record : allRecords) {
                if (record.getExpireTime() != null && now > record.getExpireTime()) {
                    localKvStoreMapper.deleteById(record.getKey());
                }
            }
            
            // 清理内存缓存（简单策略：定期全部清理）
            methodCache.clear();
            
        } catch (Exception e) {
            log.error("Error cleaning up expired entries: {}", e.getMessage());
        }
    }
}