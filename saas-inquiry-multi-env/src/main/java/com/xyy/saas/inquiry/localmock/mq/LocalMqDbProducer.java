package com.xyy.saas.inquiry.localmock.mq;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 本地模式 RocketMQ 生产者实现
 * 参考 LocalMqDbConsumer 的设计模式和数据库操作方式
 * 
 * <p>功能特点：</p>
 * <ul>
 *   <li>将消息同时存储到本地内存队列和数据库</li>
 *   <li>使用 LocalMessageMapper 进行数据库持久化操作</li>
 *   <li>支持同步和异步消息发送模式</li>
 *   <li>包含适当的日志记录和错误处理</li>
 *   <li>与 LocalMqDbConsumer 使用相同的数据库表结构</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
@Profile("local")
public class LocalMqDbProducer {

    @Autowired
    private LocalMessageMapper messageMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 本地内存队列，用于快速访问
    private final Map<String, BlockingQueue<Object>> topicQueues = new ConcurrentHashMap<>();
    
    // 异步发送线程池
    private final ThreadPoolExecutor asyncExecutor = new ThreadPoolExecutor(
        2, 10, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(1000),
        r -> new Thread(r, "LocalMqDbProducer-Async-" + System.currentTimeMillis())
    );

    /**
     * 同步发送消息（支持 MessageExt）
     * 将消息同时存储到内存队列和数据库
     *
     * @param message RocketMQ 消息对象（MessageExt）
     * @return 发送结果
     */
    @Transactional
    public SendResult sendMessage(MessageExt message) {
        try {
            log.info("【本地模式】同步发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());

            // 创建消息记录
            LocalMessagePO messagePO = createMessagePO(message);
            messagePO.setStatus("NEW");
            messagePO.setCreatedAt(LocalDateTime.now());
            messagePO.setUpdatedAt(LocalDateTime.now());

            // 持久化到数据库
            messageMapper.insert(messagePO);
            log.debug("【本地模式】消息已持久化到数据库，ID: {}", messagePO.getId());

            // 添加到内存队列
            addToMemoryQueue(message.getTopic(), message);

            // 更新状态为待处理
            messagePO.setStatus("UNPROCESSED");
            messagePO.setUpdatedAt(LocalDateTime.now());
            messageMapper.updateById(messagePO);

            // 设置消息ID到原始消息对象
            message.setMsgId(messagePO.getId().toString());

            // 创建成功的发送结果
            SendResult sendResult = new SendResult();
            sendResult.setSendStatus(SendStatus.SEND_OK);
            sendResult.setMsgId(messagePO.getId().toString());
            sendResult.setMessageQueue(null); // 本地模式不需要真实队列信息

            log.info("【本地模式】消息发送成功，消息ID: {}", messagePO.getId());
            return sendResult;

        } catch (Exception e) {
            log.error("【本地模式】同步发送消息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 同步发送消息（兼容 Message 类型）
     * 将消息同时存储到内存队列和数据库
     *
     * @param message RocketMQ 消息对象
     * @return 发送结果
     */
    @Transactional
    public SendResult sendMessage(Message message) {
        try {
            log.info("【本地模式】同步发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());
            
            // 创建消息记录
            LocalMessagePO messagePO = createMessagePO(message);
            messagePO.setStatus("NEW");
            messagePO.setCreatedAt(LocalDateTime.now());
            messagePO.setUpdatedAt(LocalDateTime.now());
            
            // 持久化到数据库
            messageMapper.insert(messagePO);
            log.debug("【本地模式】消息已持久化到数据库，ID: {}", messagePO.getId());
            
            // 添加到内存队列
            addToMemoryQueue(message.getTopic(), message);
            
            // 更新状态为待处理
            messagePO.setStatus("UNPROCESSED");
            messagePO.setUpdatedAt(LocalDateTime.now());
            messageMapper.updateById(messagePO);
            
            // 创建成功的发送结果
            SendResult sendResult = new SendResult();
            sendResult.setSendStatus(SendStatus.SEND_OK);
            sendResult.setMsgId(messagePO.getId().toString());
            sendResult.setMessageQueue(null); // 本地模式不需要真实队列信息
            
            log.info("【本地模式】消息发送成功，消息ID: {}", messagePO.getId());
            return sendResult;
            
        } catch (Exception e) {
            log.error("【本地模式】同步发送消息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 异步发送消息（支持 MessageExt）
     * 使用线程池异步处理消息发送
     *
     * @param message RocketMQ 消息对象（MessageExt）
     * @param sendCallback 发送回调
     */
    public void sendMessageAsync(MessageExt message, SendCallback sendCallback) {
        log.info("【本地模式】异步发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());

        CompletableFuture.supplyAsync(() -> {
            try {
                return sendMessage(message);
            } catch (Exception e) {
                log.error("【本地模式】异步发送消息失败: {}", e.getMessage(), e);
                return new SendResult();
            }
        }, asyncExecutor).whenComplete((result, throwable) -> {
            if (sendCallback != null) {
                if (throwable != null) {
                    sendCallback.onException(throwable);
                } else if (result.getSendStatus() == SendStatus.SEND_OK) {
                    sendCallback.onSuccess(result);
                } else {
                    sendCallback.onException(new RuntimeException("Send failed with status: " + result.getSendStatus()));
                }
            }
        });
    }

    /**
     * 异步发送消息（兼容 Message 类型）
     * 使用线程池异步处理消息发送
     *
     * @param message RocketMQ 消息对象
     * @param sendCallback 发送回调
     */
    public void sendMessageAsync(Message message, SendCallback sendCallback) {
        log.info("【本地模式】异步发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());
        
        CompletableFuture.supplyAsync(() -> {
            try {
                return sendMessage(message);
            } catch (Exception e) {
                log.error("【本地模式】异步发送消息失败: {}", e.getMessage(), e);
                return new SendResult();
            }
        }, asyncExecutor).whenComplete((result, throwable) -> {
            if (sendCallback != null) {
                if (throwable != null) {
                    sendCallback.onException(throwable);
                } else if (result.getSendStatus() == SendStatus.SEND_OK) {
                    sendCallback.onSuccess(result);
                } else {
                    sendCallback.onException(new RuntimeException("Send failed with status: " + result.getSendStatus()));
                }
            }
        });
    }

    /**
     * 单向发送消息（发送后不关心结果）- 支持 MessageExt
     *
     * @param message RocketMQ 消息对象（MessageExt）
     */
    public void sendMessageOneway(MessageExt message) {
        log.info("【本地模式】单向发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());

        asyncExecutor.execute(() -> {
            try {
                sendMessage(message);
            } catch (Exception e) {
                log.warn("【本地模式】单向发送消息失败（忽略）: {}", e.getMessage());
            }
        });
    }

    /**
     * 单向发送消息（发送后不关心结果）- 兼容 Message
     *
     * @param message RocketMQ 消息对象
     */
    public void sendMessageOneway(Message message) {
        log.info("【本地模式】单向发送消息到 topic: {}, tag: {}", message.getTopic(), message.getTags());
        
        asyncExecutor.execute(() -> {
            try {
                sendMessage(message);
            } catch (Exception e) {
                log.warn("【本地模式】单向发送消息失败（忽略）: {}", e.getMessage());
            }
        });
    }

    /**
     * 创建消息持久化对象（支持 MessageExt）
     *
     * @param message RocketMQ 消息对象（MessageExt）
     * @return LocalMessagePO 实例
     */
    private LocalMessagePO createMessagePO(MessageExt message) {
        LocalMessagePO messagePO = new LocalMessagePO();
        messagePO.setTopic(message.getTopic());
        messagePO.setTag(message.getTags());

        // 序列化消息体
        try {
            if (message.getBody() != null) {
                String messageBody = new String(message.getBody());
                messagePO.setMessageBody(messageBody);
            }
        } catch (Exception e) {
            log.warn("【本地模式】消息体序列化失败，使用默认值: {}", e.getMessage());
            messagePO.setMessageBody("{}");
        }

        // 设置消费者组（如果有的话）
        messagePO.setConsumerGroup("default_consumer_group");

        // 处理延时消息
        if (message.getDelayTimeMs() > 0) {
            log.debug("【本地模式】检测到延时消息，延时时间: {}ms", message.getDelayTimeMs());
            // 可以在这里处理延时逻辑，比如设置特殊状态或延时字段
        }

        // 处理消息键
        if (message.getKeys() != null) {
            log.debug("【本地模式】消息包含键: {}", message.getKeys());
            // 可以将键信息存储到消息体或其他字段中
        }

        return messagePO;
    }

    /**
     * 创建消息持久化对象（兼容 Message）
     *
     * @param message RocketMQ 消息对象
     * @return LocalMessagePO 实例
     */
    private LocalMessagePO createMessagePO(Message message) {
        LocalMessagePO messagePO = new LocalMessagePO();
        messagePO.setTopic(message.getTopic());
        messagePO.setTag(message.getTags());
        
        // 序列化消息体
        try {
            if (message.getBody() != null) {
                String messageBody = new String(message.getBody());
                messagePO.setMessageBody(messageBody);
            }
        } catch (Exception e) {
            log.warn("【本地模式】消息体序列化失败，使用默认值: {}", e.getMessage());
            messagePO.setMessageBody("{}");
        }
        
        // 设置消费者组（如果有的话）
        messagePO.setConsumerGroup("default_consumer_group");
        
        return messagePO;
    }

    /**
     * 添加消息到内存队列
     * 
     * @param topic 主题
     * @param message 消息对象
     */
    private void addToMemoryQueue(String topic, Object message) {
        BlockingQueue<Object> queue = topicQueues.computeIfAbsent(topic, 
            k -> new LinkedBlockingQueue<>());
        
        try {
            queue.put(message);
            log.debug("【本地模式】消息已添加到内存队列 {}, 当前队列大小: {}", topic, queue.size());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("【本地模式】添加消息到内存队列失败: {}", e.getMessage());
        }
    }

    /**
     * 获取指定主题的本地队列（用于测试或监控）
     * 
     * @param topic 主题名称
     * @return 本地队列
     */
    public BlockingQueue<Object> getLocalQueue(String topic) {
        return topicQueues.get(topic);
    }

    /**
     * 清空所有本地队列
     */
    public void clearAllQueues() {
        topicQueues.clear();
        log.info("【本地模式】已清空所有本地消息队列");
    }

    /**
     * 获取队列统计信息
     * 
     * @return 队列统计信息
     */
    public Map<String, Integer> getQueueStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        topicQueues.forEach((topic, queue) -> stats.put(topic, queue.size()));
        return stats;
    }

    /**
     * 销毁方法，清理资源
     */
    public void destroy() {
        log.info("【本地模式】LocalMqDbProducer 正在销毁...");
        
        // 关闭线程池
        asyncExecutor.shutdown();
        try {
            if (!asyncExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                asyncExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            asyncExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 清空队列
        clearAllQueues();
        
        log.info("【本地模式】LocalMqDbProducer 销毁完成");
    }
}
