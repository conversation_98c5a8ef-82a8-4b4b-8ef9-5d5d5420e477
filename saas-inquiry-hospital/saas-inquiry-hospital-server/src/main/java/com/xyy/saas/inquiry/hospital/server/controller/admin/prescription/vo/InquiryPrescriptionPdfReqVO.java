package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 处方pdf处理 VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionPdfReqVO {

    @Schema(description = "处方ids")
    @NotEmpty
    private List<Long> ids;

    private String maxSizeKey;

    private String operate;

}