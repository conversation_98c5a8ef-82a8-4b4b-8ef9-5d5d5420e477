package com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


/**
 * 处方记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPrescriptionMapper extends BaseMapperX<InquiryPrescriptionDO> {

    default PageResult<InquiryPrescriptionDO> selectPage(InquiryPrescriptionPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryPrescriptionDO> wapper = getQueryWapper(reqVO);
        // 根据 queryScene 动态选择排序字段
        if (reqVO.getQueryScene() != null) {
            wapper.orderByDesc(QuerySceneEnum.getEnumByCode(reqVO.getQueryScene()).getSortField());
        } else {
            // 默认根据开方时间排序
            wapper.orderByDesc(QuerySceneEnum.DRUGSTORE.getSortField());
        }
        return selectPage(reqVO, wapper);
    }

    default List<InquiryPrescriptionDO> selectList(InquiryPrescriptionPageReqVO reqVO) {
        return selectList(getQueryWapper(reqVO));
    }

    private LambdaQueryWrapperX<InquiryPrescriptionDO> getQueryWapper(InquiryPrescriptionPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryPrescriptionDO> wrapper = new LambdaQueryWrapperX<InquiryPrescriptionDO>()
            .inIfPresent(InquiryPrescriptionDO::getId, reqVO.getIds())
            .eqIfPresent(InquiryPrescriptionDO::getPref, reqVO.getPref())
            .eqIfPresent(InquiryPrescriptionDO::getPatientPref, reqVO.getPatientPref())
            .eqIfPresent(InquiryPrescriptionDO::getHospitalPref, reqVO.getHospitalPref())
            .eqIfPresent(InquiryPrescriptionDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryPrescriptionDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InquiryPrescriptionDO::getAuditorType, reqVO.getAuditorType())
            .eqIfPresent(InquiryPrescriptionDO::getThirdPrescriptionNo, reqVO.getThirdPrescriptionNo())
            .eqIfPresent(InquiryPrescriptionDO::getDistributeStatus, reqVO.getDistributeStatus())
            .eqIfPresent(InquiryPrescriptionDO::getDistributeUserId, reqVO.getDistributeUserId())
            .eqIfPresent(InquiryPrescriptionDO::getMedicineType, reqVO.getMedicineType())
            .betweenIfPresent(InquiryPrescriptionDO::getOutPrescriptionTime, reqVO.getOutPrescriptionTime())
            .betweenIfPresent(InquiryPrescriptionDO::getAuditPrescriptionTime, reqVO.getAuditPrescriptionTime())
            .eqIfPresent(InquiryPrescriptionDO::getInquiryWayType, reqVO.getInquiryWayType())
            .eqIfPresent(InquiryPrescriptionDO::getAutoInquiry, reqVO.getAutoInquiry())
            .eqIfPresent(InquiryPrescriptionDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(InquiryPrescriptionDO::getClientChannelType, reqVO.getClientChannelType())
            .eqIfPresent(InquiryPrescriptionDO::getBizChannelType, reqVO.getBizChannelType())
            .eqIfPresent(InquiryPrescriptionDO::getPrintStatus, reqVO.getPrintStatus());
        // 药师查审方记录单独处理
        if (ObjectUtil.equals(QuerySceneEnum.PHARMACIST.getCode(), reqVO.getQueryScene())) {

            if (StringUtils.isNotBlank(reqVO.getPharmacistPref()) && ObjectUtil.isNotEmpty(reqVO.getTargetTenantId())) {
                // 查药师 and 目标门店
                wrapper.eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref())
                    .eqIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTargetTenantId());

            } else if (StringUtils.isNotBlank(reqVO.getPharmacistPref()) && ObjectUtil.isNotEmpty(reqVO.getTenantId())) {
                // 查药师 or 所在门店
                wrapper.and(wp -> wp.eq(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref())
                    .or(w -> w.eq(InquiryPrescriptionDO::getTenantId, reqVO.getTenantId()).and(p -> p.ne(InquiryPrescriptionDO::getPharmacistPref, ""))));

            } else if (StringUtils.isNotBlank(reqVO.getPharmacistPref())) {
                // 药师编码不为空
                wrapper.eq(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref());
            } else if (ObjectUtil.isNotEmpty(reqVO.getTenantId())) {
                // 门店编码不为空
                wrapper.eq(InquiryPrescriptionDO::getTenantId, reqVO.getTenantId()).and(w -> w.ne(InquiryPrescriptionDO::getPharmacistPref, ""));
            }
        } else {
            wrapper
                .eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref())
                .eqIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTenantId());
        }
        return wrapper;
    }

    default Long getPrescriptionCount(InquiryPrescriptionPageReqVO build) {
        return selectCount(new LambdaQueryWrapperX<InquiryPrescriptionDO>().inIfPresent(InquiryPrescriptionDO::getTenantId, build.getTenantIds()).eqIfPresent(InquiryPrescriptionDO::getTenantId, build.getTenantId())
            .eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, build.getPharmacistPref()).eqIfPresent(InquiryPrescriptionDO::getAuditorType, build.getAuditorType())
            .eqIfPresent(InquiryPrescriptionDO::getDistributeStatus, build.getDistributeStatus()).eqIfPresent(InquiryPrescriptionDO::getMedicineType, build.getMedicineType())
            .inIfPresent(InquiryPrescriptionDO::getMedicineType, build.getMedicineTypes()).eqIfPresent(InquiryPrescriptionDO::getStatus, build.getStatus())
            .betweenIfPresent(InquiryPrescriptionDO::getOutPrescriptionTime, build.getOutPrescriptionTime()).betweenIfPresent(InquiryPrescriptionDO::getAuditPrescriptionTime, build.getAuditPrescriptionTime())
            .betweenIfPresent(InquiryPrescriptionDO::getCreateTime, build.getCreateTime()));
    }

    default InquiryPrescriptionDO queryByCondition(InquiryPrescriptionQueryDTO queryDTO) {
        return selectOne(new LambdaQueryWrapperX<InquiryPrescriptionDO>().eqIfPresent(InquiryPrescriptionDO::getPref, queryDTO.getPref()).eqIfPresent(InquiryPrescriptionDO::getInquiryPref, queryDTO.getInquiryPref())
            .inIfPresent(InquiryPrescriptionDO::getTenantId, queryDTO.getTenantIds()).eqIfPresent(InquiryPrescriptionDO::getTenantId, queryDTO.getTenantId())
            .eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, queryDTO.getPharmacistPref()).eqIfPresent(InquiryPrescriptionDO::getAuditorType, queryDTO.getAuditorType())
            .eqIfPresent(InquiryPrescriptionDO::getDistributeStatus, queryDTO.getDistributeStatus()).eqIfPresent(InquiryPrescriptionDO::getMedicineType, queryDTO.getMedicineType())
            .inIfPresent(InquiryPrescriptionDO::getMedicineType, queryDTO.getMedicineTypes()).eqIfPresent(InquiryPrescriptionDO::getStatus, queryDTO.getStatus()).orderByAsc(InquiryPrescriptionDO::getId), false);
    }

    int distributePharmacist(@Param("id") Long id, @Param("userId") Long userId, @Param("status") Integer status);

    int releasePharmacist(@Param("id") Long id, @Param("userId") Long userId, @Param("status") Integer status);

    IPage<PatientSimpleDTO> getPrescriptionPatientList(Page<InquiryPrescriptionPageReqVO> basePage, @Param("reqVo") InquiryPrescriptionPageReqVO reqVo);

    @Select("select inquiry_pref from saas_inquiry_prescription where tenant_id = #{tenantId} and status = 2 and inquiry_biz_type = 2 and deleted = false")
    List<String> selectRemoteAuditInquriyPrefList(Long tenantId);

    /**
     * 批量修改打印状态 - 打印，导出
     *
     * @param ids
     * @param status
     * @return
     */
    default int batchUpdatePrintStatus(List<Long> ids, Integer status) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }
        return update(new UpdateWrapper<InquiryPrescriptionDO>()
            .set("print_status", status)
            .in("id", ids));
    }

}