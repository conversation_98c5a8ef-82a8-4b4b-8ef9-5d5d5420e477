package com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionLongTermEnum;
import com.xyy.saas.inquiry.enums.prescription.external.ExternalOutFlowStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.external.ExternalPrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.external.ExternalPrescriptionCategoryEnum;
import com.xyy.saas.inquiry.enums.prescription.external.ExternalPrescriptionTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.external.MedicarePrescriptionStatusEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExternalExtDto;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 外配(电子)处方记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_prescription_external", autoResultMap = true)
@KeySequence("saas_prescription_external_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasPrescriptionExternalDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 外配处方编号
     */
    private String pref;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 业务类型 0-问诊,1-智慧脸... {@link com.xyy.saas.inquiry.enums.system.BizTypeEnum}
     */
    private Integer bizType;
    /**
     * 三方业务渠道 0荷叶,1智慧脸 2海典erp 3众康云 {@link com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum}
     */
    private Integer bizChannelType;
    /**
     * 外配处方类型 0.电子处方、1.纸质处方 {@link ExternalPrescriptionTypeEnum}
     */
    private Integer externalType;
    /**
     * 外部系统处方号
     */
    private String externalRxPref;
    /**
     * 定点医疗机构编号
     */
    private String fixMedicalInstitutionsCode;
    /**
     * 定点医疗机构名称
     */
    private String fixMedicalInstitutionsName;
    /**
     * 开方时间
     */
    private LocalDateTime rxOutTime;
    /**
     * 有效截止时间
     */
    private LocalDateTime rxExpireTime;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室名称
     */
    private String deptName;
    /**
     * 医生编号
     */
    private String doctorPref;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 药师编码
     */
    private String pharmacistPref;
    /**
     * 药师姓名
     */
    private String pharmacistName;
    /**
     * 处方类别 {@link ExternalPrescriptionCategoryEnum}
     */
    private Integer rxCategory;
    /**
     * 长期处方标识 {@link PrescriptionLongTermEnum}
     */
    private Integer longTerm;
    /**
     * 电子处方平台流水号
     */
    private String electronicRxSn;
    /**
     * 电子处方外流状态 0待外流 {@link ExternalOutFlowStatusEnum}
     */
    private Integer outFlowStatus;
    /**
     * 电子处方签名验签流水号
     */
    private String rxSignVerifySn;
    /**
     * 电子处方审核业务流水号
     */
    private String rxChkBizSn;
    /**
     * 电子处方审核状态 {@link ExternalPrescriptionAuditStatusEnum}
     */
    private Integer rxChkStatus;
    /**
     * 电子处方审核时间
     */
    private LocalDateTime rxChkTime;
    /**
     * 医保处方号
     */
    private String medicareRxNo;
    /**
     * 医保处方追溯码
     */
    private String medicareRxTraceCode;
    /**
     * 医保处方状态{@link MedicarePrescriptionStatusEnum}
     */
    private Integer medicareRxStatus;
    /**
     * 外配处方扩展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private PrescriptionExternalExtDto ext;

    @JsonIgnore
    public PrescriptionExternalExtDto extGet() {
        if (ext == null) {
            ext = new PrescriptionExternalExtDto();
        }
        return ext;
    }
}