package com.xyy.saas.inquiry.hospital.server.convert.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryGrabStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.RemotePrescriptionDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelResp1VO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.IssuesPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.dto.IssuesPrescriptionConfigDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordGrabbingDoctorDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.time.LocalDateTime;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：InquiryPrescription
 * @Author: xucao
 * @Date: 2024/10/28 17:57
 * @Description: 问诊处方转化器
 */
@Mapper
public interface InquiryPrescriptionConvert {

    InquiryPrescriptionConvert INSTANCE = Mappers.getMapper(InquiryPrescriptionConvert.class);

    default InquiryPrescriptionDO initPrescriptionDO(InquiryPrescriptionSaveReqVO createReqVO) {
        return initConvertVO2DO(createReqVO).setPref(PrefUtil.getPrescriptionPref());
    }

    InquiryPrescriptionDO initConvertVO2DO(InquiryPrescriptionSaveReqVO createReqVO);

    InquiryPrescriptionDO convertRemotePrescriptionDTO2DO(RemotePrescriptionDTO remotePrescriptionDTO);

    PageResult<InquiryPrescriptionRespVO> convertPage(PageResult<InquiryPrescriptionDO> pageResult);

    InquiryPrescriptionRespVO convertVO(InquiryPrescriptionDO inquiryPrescriptionDO);

    InquiryPrescriptionRespDTO convertVO2DTO(InquiryPrescriptionRespVO prescriptionRespVO);

    InquiryPrescriptionSaveReqVO convertDTO2VO(InquiryPrescriptionUpdateDTO prescriptionUpdateDTO);

    InquiryPrescriptionPageReqVO convertQueryVO(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 根据问诊记录+明细 + 医生开方信息 初始化创建问诊单
     *
     * @param inquiryRecordDto     问诊记录
     * @param inquiryRecordDetail  问诊记录明细
     * @param prescriptionIssuesVO 医生开方信息
     * @return 问诊单VO
     */
    default InquiryPrescriptionSaveReqVO convertSaveVO(InquiryRecordDto inquiryRecordDto, InquiryRecordDetailDto inquiryRecordDetail, PrescriptionIssuesVO prescriptionIssuesVO) {
        InquiryPrescriptionSaveReqVO saveReqVO = convertRecord2Prescription(inquiryRecordDto);
        PrescriptionExtDto prescriptionExtDto;
        if (inquiryRecordDto.isAutoInquiry()) {
            prescriptionExtDto = convertDrugDetail2ExtDTO(inquiryRecordDetail.getPreDrugDetail());
            saveReqVO.setMainSuit(inquiryRecordDetail.getMainSuit()).setDiagnosisName(inquiryRecordDetail.getDiagnosisName()).setDiagnosisCode(inquiryRecordDetail.getDiagnosisCode());
        } else {
            prescriptionExtDto = convertDrugDetail2ExtDTO(prescriptionIssuesVO.getInquiryProductDto());
            saveReqVO.setDoctorOsType(prescriptionIssuesVO.getClientOsType())
                .setDoctorChannelType(prescriptionIssuesVO.getClientChannelType())
                .setMainSuit(prescriptionIssuesVO.getMainSuit())
                .setDiagnosisName(prescriptionIssuesVO.getDiagnosis().stream().map(InquiryDiagnosisSimpleVO::getDiagnosisName).toList())
                .setDiagnosisCode(prescriptionIssuesVO.getDiagnosis().stream().map(InquiryDiagnosisSimpleVO::getDiagnosisCode).toList());
        }
        return saveReqVO.setStatus(PrescriptionStatusEnum.WAITING.getStatusCode()).setOutPrescriptionTime(LocalDateTime.now()).setInquiryEndTime(LocalDateTime.now()).setExt(prescriptionExtDto);
    }

    PrescriptionExtDto convertDrugDetail2ExtDTO(InquiryProductDto preDrugDetail);

    InquiryProductDto convertExtDTO2ProductDTO(PrescriptionExtDto prescriptionExtDto);

    @Mapping(target = "inquiryStartTime", source = "createTime")
    @Mapping(target = "inquiryPref", source = "pref")
    InquiryPrescriptionSaveReqVO convertRecord2Prescription(InquiryRecordDto inquiryRecordDto);


    default void fillInquiryRecordDoctor(InquiryRecordDto inquiryRecordDto, InquiryDoctorDO doctorDO) {
        inquiryRecordDto.setDoctorName(doctorDO.getName()).setDoctorPref(doctorDO.getPref());
    }

    InquiryPrescriptionRespDTO convertDO2DTO(InquiryPrescriptionDO inquiryPrescription);

    /**
     * 根据问诊和处方信息 转换签章dto
     *
     * @param prescriptionRespDTO 处方
     * @param detailRespDTOS      处方明细
     * @param doctorDO            医师信息
     * @param inquiryRecordDetail 问诊记录详情
     * @return
     */
    default PrescriptionSignatureInitDto convertSignatureInitDto(InquiryPrescriptionRespDTO prescriptionRespDTO, List<InquiryPrescriptionDetailRespDTO> detailRespDTOS, InquiryDoctorDto doctorDto, InquiryRecordDetailDto inquiryRecordDetail,
        IssuesPrescriptionConfigDto issuesPrescriptionConfigDto) {
        PrescriptionSignatureInitDto initDto = new PrescriptionSignatureInitDto();
        initDto.setPrescriptionPref(prescriptionRespDTO.getPref());
        initDto.setTemplateId(prescriptionRespDTO.getPreTempId());
        initDto.setParticipantItem(ParticipantItem.builder().userId(doctorDto.getUserId()).mobile(doctorDto.getMobile()).name(doctorDto.getName()).build());
        initDto.setParam(convertPrescriptionParamDto(prescriptionRespDTO, inquiryRecordDetail, detailRespDTOS, doctorDto));
        initDto.setSignaturePlatform(issuesPrescriptionConfigDto.getSignaturePlatform());
        initDto.setSignaturePlatformConfigId(issuesPrescriptionConfigDto.getSignaturePlatformConfigId());
        initDto.setInquiryBizType(prescriptionRespDTO.getInquiryBizType());
        initDto.setAuditorTypeEnum(AuditorTypeEnum.DOCTOR);
        initDto.setAutoInquiry(prescriptionRespDTO.getAutoInquiry());
        return initDto;
    }


    default PrescriptionParamDto convertPrescriptionParamDto(InquiryPrescriptionRespDTO prescriptionRespDTO, InquiryRecordDetailDto inquiryRecordDetail, List<InquiryPrescriptionDetailRespDTO> detailRespDTOS, InquiryDoctorDto doctorDto) {
        List<InquiryProductDetailDto> productDetailDtos = convertProductDetail(detailRespDTOS);
        InquiryProductDto inquiryProductDto = convertExtDTO2ProductDTO(prescriptionRespDTO.getExt());
        return PrescriptionParamDto.builder()
            .no(prescriptionRespDTO.getPref())
            .name(prescriptionRespDTO.getPatientName())
            .sex(SexEnum.forValue(prescriptionRespDTO.getPatientSex()).getDesc())
            .age(prescriptionRespDTO.getPatientAge())
            .idCard(inquiryRecordDetail.getPatientIdCard())
            .mobile(prescriptionRespDTO.getPatientMobile())
            .dept(prescriptionRespDTO.getDeptName())
            .allergic(CollUtil.join(inquiryRecordDetail.getAllergic(), ","))
            .medicineType(prescriptionRespDTO.getMedicineType())
            .inquiryProductDto(inquiryProductDto.setInquiryProductInfos(productDetailDtos))
            .doctorMedicareNo(doctorDto.getDoctorMedicareNo())
            .iptOtpNo(prescriptionRespDTO.getPatientPref()) // 取患者编号 为门诊号
            .build();
    }

    List<InquiryProductDetailDto> convertProductDetail(List<InquiryPrescriptionDetailRespDTO> detailRespDTOS);

    @Mapping(target = "unitName", source = "packageUnit")
    InquiryProductDetailDto convertProductDetail(InquiryPrescriptionDetailRespDTO detailRespDTO);


    default InquiryRecordGrabbingDoctorDto convertDoctorGrabbingDto(InquiryRecordDto inquiryRecordDto, InquiryDoctorDO doctorDO, PrescriptionGrabbingVO prescriptionGrabbingVO, Dept dept) {
        return InquiryRecordGrabbingDoctorDto.builder()
            .id(inquiryRecordDto.getId())
            .inquiryStatus(InquiryStatusEnum.INQUIRING.getStatusCode())
            .originalInquiryStatus(InquiryStatusEnum.QUEUING.getStatusCode())
            .autoGrabStatus(ObjectUtil.isEmpty(prescriptionGrabbingVO.getAutoGrabStatus()) ? InquiryGrabStatusEnum.NORMAL.getCode() : prescriptionGrabbingVO.getAutoGrabStatus())
            .startTime(LocalDateTime.now())
            .deptPref(dept.getDeptPref())
            .deptName(dept.getDeptName())
            .doctorPref(doctorDO.getPref())
            .doctorName(doctorDO.getName()).build();
    }


    IssuesPrescriptionRespVO convertDTO2VO(InquiryPrescriptionRespDTO prescriptionRespDTO);

    default void fillInquiryDetail(InquiryPrescriptionRespVO respVO, InquiryRecordDetailDto inquiryRecordDetail, InquiryRecordDto inquiryRecord) {
        respVO.setPatientIdCard(inquiryRecordDetail.getPatientIdCard());
        respVO.setAllergic(inquiryRecordDetail.getAllergic());
        respVO.setOfflinePrescriptions(inquiryRecordDetail.getOfflinePrescriptions());
        respVO.setHospitalName(inquiryRecord.getHospitalName());
        respVO.setLiverKidneyValue(inquiryRecordDetail.getLiverKidneyValue());
        respVO.setGestationLactationValue(inquiryRecordDetail.getGestationLactationValue());
    }

    InquiryPrescriptionExcelRespVO convertVO2ExcelVO(InquiryPrescriptionRespVO inquiryPrescriptionRespVO);

    default RemotePrescriptionAuditDto convertDTO2AuditDto(RemotePrescriptionDTO prescriptionUpdateDTO, InquiryPrescriptionDO inquiryPrescriptionDO) {
        return RemotePrescriptionAuditDto.builder()
            .prescriptionPref(inquiryPrescriptionDO.getPref())
            .costId(prescriptionUpdateDTO.getCostId())
            .remotePrescriptionDto(convertDO2SimpleDTO(inquiryPrescriptionDO))
            .build();
    }

    RemotePrescriptionDto convertDO2SimpleDTO(InquiryPrescriptionDO inquiryPrescriptionDO);

    default RemotePrescriptionAuditDto convert2BatchAuditDTO(List<InquiryPrescriptionDO> prescriptions) {
        return RemotePrescriptionAuditDto.builder()
            .remotePrescriptionDtos(prescriptions.stream().map(this::convertDO2SimpleDTO).toList())
            .build();
    }


    default RemotePrescriptionAuditDto convert2PrescriptionAuditDTO(InquiryPrescriptionDO prescriptionDO) {
        return RemotePrescriptionAuditDto.builder()
            .prescriptionPref(prescriptionDO.getPref())
            .remotePrescriptionDto(convert2AuditDTO(prescriptionDO))
            .build();
    }

    RemotePrescriptionDto convert2AuditDTO(InquiryPrescriptionDO prescriptionDO);

    List<InquiryPrescriptionExcelResp1VO> convertVO2ExcelVO1(List<InquiryPrescriptionExcelRespVO> inquiryPrescriptionExcelRespVOList);
}
