package com.xyy.saas.inquiry.hospital.api.hospital.dto;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.pojo.BaseDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:46
 */
@Data
public class InquiryHospitalRespDto extends BaseDto {

    /**
     * 主键
     */
    private Long id;
    /**
     * 医院名称
     */
    private String name;

    /**
     * 医疗机构编码
     */
    private String institutionCode;
    /**
     * 医院编码
     */
    private String pref;
    /**
     * 医院等级
     */
    private Integer level;
    /**
     * 医院地址
     */
    private String address;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 官方网站
     */
    private String website;
    /**
     * 是否有医保资质
     */
    private Integer hasMedicare;
    /**
     * 配置信息
     */
    private InquiryHospitalSettingDto setting;
    /**
     * 是否禁用
     */
    private Integer disable;

    /**
     * 获取医院使用的处方笺id列表
     *
     * @return
     */
    public List<Long> usedPresTempIdList() {
        if (this.setting == null) {
            return List.of();
        }
        Set<Long> usedPresTempIds = new HashSet<>();
        if (this.setting.getDefaultWesternPrescriptionTemplate() != null) {
            usedPresTempIds.add(this.setting.getDefaultWesternPrescriptionTemplate());
        }
        if (this.setting.getDefaultChinesePrescriptionTemplate() != null) {
            usedPresTempIds.add(this.setting.getDefaultChinesePrescriptionTemplate());
        }
        if (this.setting.getExtend() == null || CollUtil.isEmpty(this.setting.getExtend().getSpecificPrescriptionTemplates())) {
            return new ArrayList<>(usedPresTempIds);
        }
        // 特殊处方笺
        this.setting.getExtend().getSpecificPrescriptionTemplates()
            .stream().map(InquiryHospitalSpecificPrescriptionTemplateDto::getPrescriptionTemplateId)
            .filter(Objects::nonNull)
            .forEach(usedPresTempIds::add);

        return new ArrayList<>(usedPresTempIds);
    }

}
