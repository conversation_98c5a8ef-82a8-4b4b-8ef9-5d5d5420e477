package com.xyy.saas.inquiry.hospital.api.doctor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: xucao
 * @Date: 2025/2/11 14:22
 * @Description: 医生卡片消息
 */
@Data
public class InquiryDoctorCardInfoDto implements Serializable {

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "姓名，例如：李明")
    private String name;

    @Schema(description = "职称，例如：主任医生")
    private String titleName;

    @Schema(description = "医生头像")
    private String photo;

    @Schema(description = "医生医保编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String doctorMedicareNo;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    private LocalDateTime startPracticeTime;

    @Schema(description = "执业证书号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String professionalNo;

    @Schema(description = "从业时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer practiceTime;

    @Schema(description = "接诊人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String receptionNum;

    @Schema(description = "好评率", requiredMode = Schema.RequiredMode.REQUIRED, example = "99")
    private String goodCommentRate;
}
