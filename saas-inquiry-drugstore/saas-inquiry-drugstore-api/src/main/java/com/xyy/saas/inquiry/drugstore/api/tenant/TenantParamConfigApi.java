package com.xyy.saas.inquiry.drugstore.api.tenant;

import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDto;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import java.util.List;

/**
 * 门店配置Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 15:09
 */
public interface TenantParamConfigApi {


    /**
     * 批量查询门店配置
     *
     * @param tenantIds 门店ids
     * @param type      类型
     * @return 配置列表
     */
    List<TenantParamConfigDto> batchQueryTenantParamConfig(List<Long> tenantIds, TenantParamConfigTypeEnum type);

    /**
     * 查询门店配置
     *
     * @param tenantId
     * @param type
     * @return
     */
    TenantParamConfigDto queryTenantParamConfig(Long tenantId, TenantParamConfigTypeEnum type);

    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return String类型的配置值, 没有返回空, 需要StringUtils.isBlank校验
     */
    String getParamConfigValueSelf2Sys(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有-null
     */
    Integer getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);


    /**
     * 先取全局，没有取自己
     *
     * @param tenantParamConfigTypeEnum 枚举
     * @return 配置值
     */
    String getParamConfigValueSys2Self(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取全局，没有取自己
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有-null
     */
    Integer getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);


}
