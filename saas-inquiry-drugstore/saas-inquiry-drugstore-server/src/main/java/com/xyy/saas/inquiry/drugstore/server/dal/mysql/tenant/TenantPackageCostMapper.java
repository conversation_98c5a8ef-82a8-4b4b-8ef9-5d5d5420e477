package com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.cost.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店问诊套餐额度 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageCostMapper extends BaseMapperX<TenantPackageCostDO> {

    default PageResult<TenantPackageCostDO> selectPage(TenantPackageCostReqVO reqVO) {
        return null;
    }

    /**
     * 扣减用户套餐额度
     *
     * @param id              套餐额度id
     * @param surplusCost     当前余额
     * @param inquiryCostUnit 问诊额度单位,扣减次数
     * @return
     */
    int deductTenantCost(@Param("id") Long id, @Param("surplusCost") Long surplusCost, @Param("inquiryCostUnit") Long inquiryCostUnit);

    /**
     * 回血用户套餐额度
     *
     * @param id              套餐额度id
     * @param surplusCost     当前余额
     * @param inquiryCostUnit 问诊额度单位,扣减次数
     * @return
     */
    int reBackTenantCost(@Param("id") Long id, @Param("surplusCost") Long surplusCost, @Param("inquiryCostUnit") Long inquiryCostUnit);

    /**
     * 根据条件查询套餐额度
     *
     * @param reqVO
     * @return
     */
    default List<TenantPackageCostDO> queryTenantPackageCostByCondition(TenantPackageCostReqVO reqVO) {
        LambdaQueryWrapperX<TenantPackageCostDO> wrapper = new LambdaQueryWrapperX<TenantPackageCostDO>()
            .eq(CollUtil.isEmpty(reqVO.getTenantIds()), TenantPackageCostDO::getTenantId, reqVO.getTenantId() == null ? TenantContextHolder.getRequiredTenantId() : reqVO.getTenantId())
            .inIfPresent(TenantPackageCostDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(TenantPackageCostDO::getBizType, reqVO.getBizType())
            .eqIfPresent(TenantPackageCostDO::getTenantPackageId, reqVO.getTenantPackageId())
            // .eqIfPresent(TenantPackageCostDO::getPlatformReview, reqVO.getPlatformReview())
            .inIfPresent(TenantPackageCostDO::getTenantPackageId, reqVO.getTenantPackageIds())
            .inIfPresent(TenantPackageCostDO::getInquiryWayType, reqVO.getInquiryWayTypes())
            .eqIfPresent(TenantPackageCostDO::getInquiryWayType, reqVO.getInquiryWayType())
            .eqIfPresent(TenantPackageCostDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(TenantPackageCostDO::getInquiryAuditType, reqVO.getInquiryAuditType())
            .geIfPresent(TenantPackageCostDO::getEndTime, reqVO.getGeEndTime())
            .eqIfPresent(TenantPackageCostDO::getStatus, reqVO.getStatus());
        if (reqVO.isInServerTime()) {
            wrapper.le(TenantPackageCostDO::getStartTime, LocalDateTime.now()).ge(TenantPackageCostDO::getEndTime, LocalDateTime.now());
        }
        return selectList(wrapper);
    }

    int updateServerTimeByIds(@Param("costDto") TenantPackageCostDto tenantPackageCostDto, @Param("ids") List<Long> ids);


}