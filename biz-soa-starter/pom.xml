<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>local-server-cloud</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>${revision}</version>
	</parent>

	<artifactId>biz-soa-starter</artifactId>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<artifactId>saas-cloud-dependencies-bom</artifactId>
				<groupId>com.xyy.saas</groupId>
				<scope>import</scope>
				<type>pom</type>
				<version>${revision}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- spring boot starter -->
		<dependency>
			<artifactId>spring-boot-starter-web</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<!-- dubbo -->
<!-- 		<dependency> -->
<!-- 			<artifactId>dubbo-spring-boot-starter</artifactId> -->
<!-- 			<groupId>org.apache.dubbo</groupId> -->
<!-- 		</dependency> -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
		</dependency>
		<!-- registry dependency -->
<!-- 		<dependency> -->
<!-- 			<artifactId>nacos-client</artifactId> -->
<!-- 			<groupId>com.alibaba.nacos</groupId> -->
<!-- 		</dependency> -->
<!-- 		<dependency> -->
<!-- 			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId> -->
<!-- 			<groupId>com.alibaba.cloud</groupId> -->
<!-- 		</dependency> -->
		<dependency>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
			<groupId>com.alibaba.cloud</groupId>
		</dependency>
		<dependency>
			<artifactId>okhttp</artifactId>
			<groupId>com.squareup.okhttp3</groupId>
			<version>3.14.9</version>
		</dependency>
		<!--<dependency>
			<artifactId>fastjson2</artifactId>
			<groupId>com.alibaba.fastjson2</groupId>
			<version>2.0.29</version>
		</dependency>-->
		<!-- 业务组件 -->
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-biz-ip</artifactId>
		</dependency>
		<!-- Web 相关  -->
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-protection</artifactId>
		</dependency>

		<!-- DB 相关 -->
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-mybatis</artifactId>
		</dependency>

		<!-- 分布式相关 -->
		<dependency>
			<groupId>com.xyy.saas</groupId>
			<artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-redis</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>redisson-spring-boot-starter</artifactId>
					<groupId>org.redisson</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Test 测试相关 -->
		<dependency>
			<groupId>cn.iocoder.boot</groupId>
			<artifactId>yudao-spring-boot-starter-test</artifactId>
		</dependency>

		<!--<dependency>
			<artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
			<groupId>org.springframework.cloud</groupId>
		</dependency>
		<dependency>
			<artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-reload4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
			<groupId>org.apache.dubbo</groupId>
			<type>pom</type>
			<version>${dubbo.version}</version>
		</dependency>
		<dependency>
			<artifactId>curator-recipes</artifactId>
			<groupId>org.apache.curator</groupId>
			<version>5.1.0</version>
		</dependency>-->

	</dependencies>


</project>
