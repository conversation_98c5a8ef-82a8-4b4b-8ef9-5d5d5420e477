package com.xyy.saas.datasync.sqlite.support;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlLiteSyntaxConverter {

    /**
     * MySQL 语法转换为 SQLite 语法
     * 处理建表语句和查询语句的转换
     */
    public static String convert(String sql) {
        // 去掉注释
        sql = sql.replaceAll("(?m)^#(.*)$", "");

        // 如果是建表语句，进行 DDL 转换
        if (sql.trim().toUpperCase().startsWith("CREATE TABLE") || sql.trim().toUpperCase().startsWith("DROP TABLE") ) {
            return convertDDL(sql);
        }
        // 否则进行 DML 转换
        return convertDML(sql);
    }

    /**
     * 转换建表语句 (DDL)
     */
    private static String convertDDL(String sql) {
        // 1. 彻底移除所有注释（分四步处理）
        // 步骤1：移除字段注释（comment 'xxx' 形式）
        sql = sql.replaceAll("(?i)\\s+comment\\s+('[^']*'|\"[^\"]*\")", "");

        // 步骤2：移除表注释（comment='xxx' 形式）
        sql = sql.replaceAll("(?i)\\bcomment\\s*=\\s*('[^']*'|\"[^\"]*\")", "");

        // 步骤3：移除行尾注释（-- 或 # 开头）
        sql = sql.replaceAll("(?m)--.*$", "");
        sql = sql.replaceAll("(?m)#.*$", "");

        // 步骤4：移除块注释（/*...*/）
        sql = sql.replaceAll("/\\*.*?\\*/", "");

        // 2. 处理字段分隔符（严格替换所有||）
        sql = sql.replaceAll("\\s*\\|\\|\\s*", ",")
                .replaceAll(",\\s*,", ",")     // 处理多余逗号
                .replaceAll("(,)\\s*\\)", ")"); // 处理结尾逗号

        // 3. 转换数据类型和语法
        return sql.replaceAll("(?i)AUTO_INCREMENT", "")
                .replaceAll("\\bbit\\b", "INTEGER")
                .replaceAll("b'([01])'", "$1")
                .replaceAll("(?i)datetime", "TEXT")
                .replaceAll("(?i)unique key (\\w+)\\s*\\((.+?)\\)", "CONSTRAINT $1 UNIQUE ($2)")
                .replaceAll("`", "\"")
                .replaceAll("(?i)engine\\s*=\\s*\\w+.*", "")
                // 处理其他整数类型转换（如int(11)）
                .replaceAll("(?i)\\b(tinyint|smallint|mediumint|int|bigint|bigint unsigned)\\b(\\(\\d+\\))?", "INTEGER")
                // 移除ON UPDATE子句
                .replaceAll("(?i)\\s+on\\s+update\\s+CURRENT_TIMESTAMP", "")
//                // 包裹带连字符的标识符
//                .replaceAll("\\b([a-zA-Z0-9_]+-[a-zA-Z0-9_]+)\\b", "\"$1\"")
//                // 包裹所有标识符（最终兜底）
//                .replaceAll("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\b(?!\\()", "\"$1\"")
                // 最终兜底：移除残留的comment关键字
                .replaceAll("\\bcomment\\b", "");
    }

    /**
     * 转换查询语句 (DML)
     */
    private static String convertDML(String sql) {
        // 1. 分页语法转换
        sql = convertLimitOffset(sql);

        // 2. 函数替换
        sql = sql.replaceAll("CONCAT\\$(.+?)\\$", "($1)")
                .replaceAll(",", " || ");

        // 3. 日期函数转换
        sql = sql.replaceAll("NOW\\$\\$", "CURRENT_TIMESTAMP")
                .replaceAll("DATE_FORMAT\\((.+?),\\s*'%Y-%m-%d'\\)", "strftime('%Y-%m-%d', $1)")
                .replaceAll("DATE_FORMAT\\((.+?),\\s*'%Y-%m-%d %H:%i:%s'\\)", "strftime('%Y-%m-%d %H:%M:%S', $1)");

        return sql;
    }

    /**
     * 转换分页语法
     */
    private static String convertLimitOffset(String sql) {
        Pattern pattern = Pattern.compile("LIMIT\\s+(\\d+)\\s*,\\s*(\\d+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        if (matcher.find()) {
            String offset = matcher.group(1);
            String size = matcher.group(2);
            return matcher.replaceAll("LIMIT " + size + " OFFSET " + offset);
        }
        return sql;
    }
}
