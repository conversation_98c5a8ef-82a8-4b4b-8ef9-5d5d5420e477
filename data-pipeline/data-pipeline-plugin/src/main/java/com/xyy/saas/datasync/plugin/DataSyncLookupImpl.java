package com.xyy.saas.datasync.plugin;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.CaseInsensitiveMap;
import cn.hutool.core.map.MapUtil;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import com.xyy.saas.datasync.client.worker.DataSyncLookup;
import com.xyy.saas.localserver.utils.DbUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/03 14:40
 */
//@DubboService(group = "${dubbo.application.name}")
public class DataSyncLookupImpl<T> implements DataSyncLookup {

	private final Environment environment;

	private final JdbcTemplate jdbcTemplate;

	private final ObjectProvider<DataTableDmlTransformer> dataTableDmlTransformerProvider;

	public DataSyncLookupImpl(Environment environment, JdbcTemplate jdbcTemplate,
							  ObjectProvider<DataTableDmlTransformer> dataTableDmlTransformerProvider) {
		this.environment = environment;
		this.jdbcTemplate = jdbcTemplate;
		this.dataTableDmlTransformerProvider = dataTableDmlTransformerProvider;
	}

	/**
	 * 获取同步表名
	 *
	 * @return 服务器名, 表名
	 */
	@Override
	public Map<String, List<String>> getServerTableNames() {
		String applicationName = environment.getProperty("spring.application.name");
		return MapUtil.builder(applicationName, DataSyncEntityContext.getTableNames()).map();
	}

	/**
	 * TODO 做一层缓存
	 * @param tableName 表名
	 * @return
	 */
	public List<String> getColumns(String tableName) {
		DataSyncEntityDefinition<?> dataSyncEntityDefinition = DataSyncEntityContext.getTableEntityMap().get(tableName);
		if (dataSyncEntityDefinition == null) {
			throw new RuntimeException("getColumns tableName:" + tableName + ",表不存在");
		}
		//实现根据表名获取列名，直接查询数据库获取
		String sql = "select COLUMN_NAME from information_schema.columns where TABLE_SCHEMA = database() AND table_name = '" + tableName + "'";
		List<String> columns = jdbcTemplate.queryForList(sql, String.class);
		return columns.stream().map(String::toLowerCase).toList();
	}

	@Override
	public Map<String, List<TableColumn>> getTableInfo(List<String> tableNameList) {
		if (CollectionUtil.isEmpty(tableNameList)) {
			return Map.of();
		}
		//实现根据表名获取列名，直接查询数据库获取
		String sql = String.format("SELECT TABLE_NAME, ORDINAL_POSITION, COLUMN_NAME, COLUMN_TYPE, COLUMN_KEY, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, EXTRA, COLUMN_COMMENT " +
									   " FROM information_schema.columns WHERE TABLE_SCHEMA = database() AND table_name IN (%s) ORDER BY TABLE_NAME, ORDINAL_POSITION",
								   tableNameList.stream().map(tableName -> "?").collect(Collectors.joining(",")));

		// 结果集映射
		RowMapper<TableColumn> rowMapper = (rs, rowNum) -> new TableColumn(
			rs.getString("TABLE_NAME").toLowerCase(),
			rs.getInt("ORDINAL_POSITION"),
			rs.getString("COLUMN_NAME"),
			rs.getString("COLUMN_TYPE"),
			rs.getString("DATA_TYPE"),
			rs.getString("COLUMN_COMMENT"),
			"PRI".equalsIgnoreCase(rs.getString("COLUMN_KEY")),
			"auto_increment".equalsIgnoreCase(rs.getString("EXTRA")),
			rs.getString("COLUMN_DEFAULT"),
			"YES".equalsIgnoreCase(rs.getString("IS_NULLABLE")),
			rs.getInt("CHARACTER_MAXIMUM_LENGTH"),
			rs.getInt("NUMERIC_PRECISION"),
			rs.getInt("NUMERIC_SCALE")
		);

		return jdbcTemplate.query(sql, rowMapper, tableNameList.toArray())
			.stream().collect(Collectors.groupingBy(TableColumn::tableName));
	}

	/**
	 * @param biz         业务线  ,目前业务线是null
	 * @param tenantId
	 * @param tableName   表名
	 * @param baseVersion 最大版本号
	 * @return
	 */
	@Override
	public DataPullVO invokerLookup(String biz, String tenantId, String tableName, Long baseVersion) {
		DataSyncEntityDefinition<?> dataSyncEntityDefinition = DataSyncEntityContext.getTableEntityMap().get(tableName);
		if (dataSyncEntityDefinition == null) {
			throw new RuntimeException("invokerLookup tableName:" + tableName + ",表不存在");
		}
//		Class<T> entityClazz = dataSyncEntityDefinition.getClazz();
		boolean hasTenant = dataTableDmlTransformerProvider.getIfAvailable()
			.checkTableColumnExist(tableName, DataSyncUtil.TENANT_ID);

		String tenantSQL = hasTenant ? " and " + DataSyncUtil.TENANT_ID + "  = " + tenantId : "";

		// 租户表同步数据（当前租户和下级租户）
		if (DbUtils.isTenantDB(tableName)) {
			tenantSQL += " and id = " + DataSyncUtil.PRIMARY_KEY + " OR " + DataSyncUtil.HEAD_TENANT_ID + " = " + tenantId;
		}

		String sql = "select * from " + tableName + " where " +
			DataSyncUtil.BASE_VERSION + " > " + baseVersion + tenantSQL + " order by " + DataSyncUtil.BASE_VERSION + " limit 1000";
//		List<T> objects = jdbcTemplate.queryForList(sql, entityClazz);
		List<Map<String, Object>> detailValues = jdbcTemplate.queryForList(sql);
		return DataPullVO.builder().data(detailValues).build();
	}

	/**
	 * @param tenantId
	 * @param dataPushVO
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Throwable.class)
	public List<Long> invokerPush(String tenantId, DataPushVO dataPushVO) {
		String tableName = dataPushVO.getTableName();
		DataSyncEntityDefinition<?> dataSyncEntityDefinition = DataSyncEntityContext.getTableEntityMap().get(tableName);
		if (dataSyncEntityDefinition == null) {
			throw new RuntimeException("invokerPush tableName:" + tableName + ",表不存在");
		}

		return CollectionUtil.unionAll(
			batchInsert(tableName, dataPushVO.getInsertList()),
			batchUpdate(tableName, dataPushVO.getUpdateList()),
			batchDelete(tableName, dataPushVO.getDeleteList())
		);
	}

	/**
	 * 获取&校验 表的列名
	 * @param tableName
	 * @param dataList
	 * @return
	 */
	private List<String> getAndCheckColumns(String tableName, List<Map<String,Object>> dataList) {
		// 获取表的列名
		List<String> columns = getColumns(tableName);
		//这里需要判断一下dataList中的列是否在columns中存在
		List<String> dataListColumns = new ArrayList<>(dataList.get(0).keySet());
		// 列名不区分大小写
		dataListColumns = dataListColumns.stream().map(String::toLowerCase).toList();
		columns = columns.stream().map(String::toLowerCase).toList();
		if (!CollectionUtil.containsAll(columns, dataListColumns)) {
			throw new RuntimeException("invokerPush tableName:" + tableName + ",dataList:" + dataListColumns + " 中的列不在columns中: " + columns);
		}
		return columns;
	}

	/**
	 * 批量插入
	 * @param tableName
	 * @param dataList
	 * @return
	 */
	private List<Long> batchInsert(String tableName, List<Map<String, Object>> dataList) {
		if (CollectionUtil.isEmpty(dataList)) {
			return List.of();
		}
		List<String> columns = getAndCheckColumns(tableName, dataList);

		// 构建插入SQL语句
		String insertSql = String.format("INSERT INTO %s (%s) VALUES (%s)", DbUtils.addBackquote(tableName),
										 String.join(",", columns.stream().map(DbUtils::addBackquote).toList()),
										 columns.stream().map(c -> "?").collect(Collectors.joining(",")));

		// 使用KeyHolder来获取生成的主键ID
		KeyHolder keyHolder = new GeneratedKeyHolder();

		// 使用batchUpdate进行批量插入
		jdbcTemplate.batchUpdate(
			con -> con.prepareStatement(insertSql, PreparedStatement.RETURN_GENERATED_KEYS),
			new BatchPreparedStatementSetter() {
				@Override
				public void setValues(PreparedStatement ps, int i) throws SQLException {
					// 转换成key不区分大小写的map
					Map<String, Object> data = new CaseInsensitiveMap<>(dataList.get(i));
					int index = 1;
					for (String column : columns) {
						ps.setObject(index++, data.getOrDefault(column, null));
					}
				}
				@Override
				public int getBatchSize() {
					return dataList.size();
				}
			},
			keyHolder);

		// 获取生成的主键ID列表
		List<Map<String, Object>> keyList = keyHolder.getKeyList();
		List<Long> ids = new ArrayList<>();
		for (Map<String, Object> keyMap : keyList) {
			ids.add(((Number) keyMap.values().iterator().next()).longValue());
		}
		// 上传的数据已有id
		dataList.forEach(data -> {
			Optional.ofNullable(data.get(DataSyncUtil.PRIMARY_KEY))
				.map(x -> Long.valueOf(x.toString()))
				.ifPresent(ids::add);
		});
		return CollectionUtil.distinct(ids);
	}

	/**
	 * 批量更新
	 * @param tableName
	 * @param dataList  需要传表中全部字段, 否则会update null
	 * @return
	 */
	private List<Long> batchUpdate(String tableName, List<Map<String, Object>> dataList) {
		if (CollectionUtil.isEmpty(dataList)) {
			return List.of();
		}

		List<String> columns = getAndCheckColumns(tableName, dataList);

		// update主键不做修改
		columns.remove(DataSyncUtil.PRIMARY_KEY);
		// 构建SQL语句
		String updateSql = String.format("UPDATE %s SET %s WHERE %s = ?", tableName,
										 columns.stream().map(c -> c + "=?").collect(Collectors.joining(",")),
										 DataSyncUtil.PRIMARY_KEY);

		List<Long> ids = new ArrayList<>();
		// 使用batchUpdate进行批量插入
		jdbcTemplate.batchUpdate(
			updateSql,
			new BatchPreparedStatementSetter() {
				@Override
				public void setValues(PreparedStatement ps, int i) throws SQLException {
					// 转换成key不区分大小写的map
					Map<String, Object> data = new CaseInsensitiveMap<>(dataList.get(i));
					int index = 1;
					for (String column : columns) {
						ps.setObject(index++, data.getOrDefault(column, null));
					}
					// 取主键
					Long id = Optional.ofNullable(data.get(DataSyncUtil.PRIMARY_KEY))
						.map(x -> Long.valueOf(x.toString()))
						.orElseThrow(() -> new RuntimeException("invokerPush tableName:" + tableName + ",data:" + data + " 中没有主键"));
					ids.add(id);
					ps.setObject(index, id);
				}
				@Override
				public int getBatchSize() {
					return dataList.size();
				}
			});

		return ids;
	}

	/**
	 * 批量删除
	 * @param tableName
	 * @param dataList
	 * @return
	 */
	private List<Long> batchDelete(String tableName, List<Long> dataList) {
		if (CollectionUtil.isEmpty(dataList)) {
			return List.of();
		}
		String deleteSql = String.format("DELETE FROM %s WHERE %s IN (%s)", tableName,
										 DataSyncUtil.PRIMARY_KEY,
										 dataList.stream().map(c -> "?").collect(Collectors.joining(",")));

		// 执行删除
		int cnt = jdbcTemplate.update(deleteSql, dataList.toArray());

		if (cnt > 0) {
			return CollectionUtil.distinct(dataList);
		}
		return List.of();
	}

}
